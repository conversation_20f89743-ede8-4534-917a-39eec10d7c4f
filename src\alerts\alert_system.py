import smtplib
from email.message import EmailMessage
import requests
import json
from datetime import datetime
import firebase_admin
from firebase_admin import credentials, messaging
import os

class AlertSystem:
    def __init__(self, config_path="config/alerts.json"):
        # Initialize patient_configs dictionary first
        self.patient_configs = {}
        
        # Set default device token for testing
        self.default_device_token = "d7S14ut-QRWUPnIM7Xcg39:APA91bH8pRJDrlt18ZdbLX7SvD6dDqPDb26p1K2wMeR8IX_xHh92qCdCRbFKef02ZjqAqp30ixSf2RPlegQ88UMrtIXNkSNEozx29c2whhMQG0t-BEiQAMM"
        
        # Set default recipient email
        self.default_recipient_email = "<EMAIL>"
        
        # Set default thresholds with expanded vital signs
        self.alert_thresholds = {
            'O2 Saturation': [90, 100],
            'Heart Rate': [47, 150],
            'MAP (mmHg)': [50, 180],
            'Temperature (C)': [36.0, 39.5],
            'Respiratory Rate': [8, 30],
            'Systolic BP': [90, 160],
            'Diastolic BP': [60, 90],
            'Glucose': [70, 180],
            'GCS': [3, 15],
            'Temperature': [36.0, 39.5],
            'Temp': [36.0, 39.5],
            'SPO2': [90, 100],
            'Oxygen': [90, 100],
            'HR': [47, 150],
            'Pulse': [47, 150],
            'RR': [8, 30],
            'BP': [90, 160]
        }
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Initialize Firebase Admin SDK if push notifications are enabled
        if self.config['push']['enabled'] and self.config['push']['service'] == 'firebase':
            self._initialize_firebase()
    
    def _load_config(self, config_path):
        """Load alert configuration from JSON file"""
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                print(f"Loaded alert configuration from {config_path}")
                # Don't print the full config with sensitive data
                return config
            else:
                print(f"Config file {config_path} not found, using default configuration")
                # Return default configuration
                return {
                    "email": {
                        "enabled": True,
                        "smtp_server": "smtp.gmail.com",
                        "port": 587,
                        "username": "<EMAIL>",
                        "password": "hwfa ysce vmlt sgrr"
                    },
                    "sms": {
                        "enabled": False,
                        "api_key": "",
                        "api_url": ""
                    },
                    "push": {
                        "enabled": True,
                        "service": "firebase",
                        "project_id": "eicu-dca5d",
                        "messaging_sender_id": "************",
                        "app_id": "1:************:web:9ab7847f035c1afac76e6f"
                    }
                }
        except Exception as e:
            print(f"Error loading config: {e}")
            # Return default configuration
            return {
                "email": {
                    "enabled": True,
                    "smtp_server": "smtp.gmail.com",
                    "port": 587,
                    "username": "<EMAIL>",
                    "password": "hwfa ysce vmlt sgrr"
                },
                "sms": {
                    "enabled": False,
                    "api_key": "",
                    "api_url": ""
                },
                "push": {
                    "enabled": True,
                    "service": "firebase",
                    "project_id": "eicu-dca5d",
                    "messaging_sender_id": "************",
                    "app_id": "1:************:web:9ab7847f035c1afac76e6f"
                }
            }
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK for push notifications"""
        try:
            # Check if already initialized
            if firebase_admin._apps:
                print("Firebase already initialized")
                return True
            
            # Get the service account key file path
            service_account_path = "config/firebase-service-account.json"
            
            # Check if service account file exists
            if not os.path.exists(service_account_path):
                print(f"Firebase service account file not found at {service_account_path}")
                # Try to create a minimal service account file
                try:
                    minimal_service_account = {
                        "type": "service_account",
                        "project_id": self.config['push']['project_id'],
                        "private_key_id": "dummy_key_id",
                        "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKj\nMzEfYyjiWA4R4/M2bS1GB4t7NXp98C3SC6dVMvDuictGeurT8jNbvJZHtCSuYEvu\nNMoSfm76oqFvAp8Gy0iz5sxjZmSnXyCdPEovGhLa0VzMaQ8s+CLOyS56YyCFGeJZ\n-----END PRIVATE KEY-----\n",
                        "client_email": f"firebase-adminsdk@{self.config['push']['project_id']}.iam.gserviceaccount.com",
                        "client_id": "dummy_client_id",
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                        "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk%40{self.config['push']['project_id']}.iam.gserviceaccount.com"
                    }
                    
                    os.makedirs(os.path.dirname(service_account_path), exist_ok=True)
                    with open(service_account_path, 'w') as f:
                        json.dump(minimal_service_account, f)
                    print(f"Created minimal service account file at {service_account_path}")
                except Exception as e:
                    print(f"Error creating minimal service account file: {e}")
                    return False
            
            # Initialize the app
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred, {
                'projectId': self.config['push']['project_id']
            })
            
            print("Firebase initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing Firebase: {e}")
            return False
    
    def set_patient_alert_config(self, patient_id, contact_info, custom_thresholds=None):
        """Set alert configuration for a specific patient"""
        # If patient already has a config, update it instead of replacing
        if patient_id in self.patient_configs:
            # Update contact info
            for key, value in contact_info.items():
                self.patient_configs[patient_id]['contact_info'][key] = value
        
            # Update thresholds if provided
            if custom_thresholds:
                self.patient_configs[patient_id]['thresholds'] = custom_thresholds
        else:
            # Create new config
            self.patient_configs[patient_id] = {
                'contact_info': contact_info,
                'thresholds': custom_thresholds or self.alert_thresholds,
                'last_alert_time': {}  # To prevent alert flooding
            }
        
        print(f"Alert config set for patient {patient_id}: {contact_info}")
        return True
        
    def check_vitals(self, patient_id, vitals_data):
        """Check if any vital signs trigger an alert"""
        # If patient doesn't have a config yet, create one with default settings
        if patient_id not in self.patient_configs:
            print(f"No alert config found for patient {patient_id}, creating default config")
            # Create a default configuration for this patient
            self.set_patient_alert_config(patient_id, {}, self.alert_thresholds)
        
        patient_config = self.patient_configs[patient_id]
        alerts = []
        
        # Debug info
        print(f"Checking vitals for patient {patient_id}: {vitals_data}")
        print(f"Using thresholds: {patient_config['thresholds']}")
        
        # Check each vital sign against thresholds
        for vital, value in vitals_data.items():
            # Skip if vital is not in thresholds
            if vital not in patient_config['thresholds']:
                # Try to find a matching threshold by partial name
                matched = False
                for threshold_name in patient_config['thresholds'].keys():
                    if threshold_name.lower() in vital.lower() or vital.lower() in threshold_name.lower():
                        vital = threshold_name  # Use the matching threshold name
                        matched = True
                        break
            
                if not matched:
                    print(f"No threshold found for {vital}, skipping")
                    continue
        
            threshold = patient_config['thresholds'][vital]
            
            # Handle range thresholds [min, max]
            if isinstance(threshold, list) and len(threshold) == 2:
                if value < threshold[0]:
                    alert_msg = f"{vital} is too low: {value} (threshold: {threshold[0]})"
                    alerts.append(alert_msg)
                    print(f"Alert triggered: {alert_msg}")
                elif value > threshold[1]:
                    alert_msg = f"{vital} is too high: {value} (threshold: {threshold[1]})"
                    alerts.append(alert_msg)
                    print(f"Alert triggered: {alert_msg}")
            # Handle single thresholds (minimum)
            elif value < threshold:
                alert_msg = f"{vital} is critical: {value} (threshold: {threshold})"
                alerts.append(alert_msg)
                print(f"Alert triggered: {alert_msg}")
        
        # Send alerts if any were triggered
        if alerts:
            alert_sent = self._send_alert(patient_id, alerts)
            return alert_sent
        else:
            print("No alerts triggered")
            return False
    
    def _send_alert(self, patient_id, alert_messages):
        """Send alerts through configured channels"""
        if patient_id not in self.patient_configs:
            print(f"No alert config found for patient {patient_id}, creating default config")
            self.set_patient_alert_config(patient_id, {}, self.alert_thresholds)
        
        patient = self.patient_configs[patient_id]
        contact = patient.get('contact_info', {})
        
        # Combine alert messages
        message = f"ALERT for Patient {patient_id}:\n" + "\n".join(alert_messages)
        
        # Check cooldown to prevent alert flooding
        now = datetime.now()
        if 'last_alert_time' in patient:
            for alert_type in alert_messages:
                if alert_type in patient['last_alert_time']:
                    # Only alert if more than 15 minutes since last alert of this type
                    time_diff = (now - patient['last_alert_time'][alert_type]).total_seconds() / 60
                    if time_diff < 15:
                        continue
        
        # Update last alert time
        for alert_type in alert_messages:
            if 'last_alert_time' not in patient:
                patient['last_alert_time'] = {}
            patient['last_alert_time'][alert_type] = now
        
        # Send through enabled channels
        success = False
        
        # Email alerts - always send to default recipient
        if self.config['email']['enabled']:
            # First send to the default recipient
            if hasattr(self, 'default_recipient_email'):
                subject = f"ICU Patient Alert - Patient {patient_id}"
                success |= self._send_email(self.default_recipient_email, subject, message)
            
            # Also send to patient's email if available
            if 'email' in contact and contact['email'] != getattr(self, 'default_recipient_email', None):
                subject = f"ICU Patient Alert - Patient {patient_id}"
                success |= self._send_email(contact['email'], subject, message)
        
        # SMS alerts
        if self.config['sms']['enabled'] and 'phone' in contact:
            success |= self._send_sms(contact['phone'], message)
        
        # Push notifications - always send to default device token
        if self.config['push']['enabled']:
            # First try to send to the default device token
            success |= self._send_push(self.default_device_token, message)
            
            # Also send to patient's device token if available
            if 'device_token' in contact and contact['device_token'] != self.default_device_token:
                success |= self._send_push(contact['device_token'], message)
        
        return success
    
    def _send_email(self, recipient, subject, message):
        """Send email alert with fixed subject"""
        try:
            config = self.config['email']
            
            # Debug info
            print(f"Sending email to {recipient}")
            print(f"Using SMTP server: {config['smtp_server']}:{config['port']}")
            print(f"Using username: {config['username']}")
            
            # Create email message
            msg = EmailMessage()
            msg.set_content(message)
            msg['Subject'] = "ICU Alert"  # <- fixed subject
            msg['From'] = config['username']
            msg['To'] = recipient
            
            # Connect to SMTP server
            server = smtplib.SMTP(config['smtp_server'], config['port'])
            server.starttls()
            server.login(config['username'], config['password'])
            server.send_message(msg)
            server.quit()
            
            print("Email sent successfully")
            return True
        except Exception as e:
            print(f"Email alert failed: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False

    
    def _send_sms(self, phone_number, message):
        """Send SMS alert"""
        try:
            config = self.config['sms']
            response = requests.post(
                config['api_url'],
                json={
                    'api_key': config['api_key'],
                    'to': phone_number,
                    'message': message
                }
            )
            return response.status_code == 200
        except Exception as e:
            print(f"SMS alert failed: {e}")
            return False
    
    def _send_push(self, device_token, message):
        """Send push notification"""
        try:
            if not self.config['push']['enabled']:
                print("Push notifications are disabled in config")
                return False
            
            if self.config['push']['service'] == 'firebase':
                return self._send_firebase_push(device_token, message)
            else:
                print(f"Unsupported push notification service: {self.config['push']['service']}")
                return False
        except Exception as e:
            print(f"Push notification failed: {e}")
            import traceback
            print(traceback.format_exc())
            return False
    
    def _send_firebase_push(self, device_token, message):
        """Send Firebase Cloud Messaging push notification using Admin SDK"""
        try:
            # Clean up the device token (remove any spaces)
            device_token = device_token.strip().replace(" ", "")
            
            # Check if Firebase Admin SDK is initialized
            if not firebase_admin._apps:
                self._initialize_firebase()
                
            # Check if this is a heart rate alert
            is_urgent = "Heart Rate" in message and ("low" in message.lower() or "critical" in message.lower())
            
            # Create message
            notification = messaging.Notification(
                title='URGENT: ICU Patient Alert' if is_urgent else 'ICU Patient Alert',
                body=message
            )
            
            # Add Android-specific configuration
            android_config = messaging.AndroidConfig(
                priority='high',
                notification=messaging.AndroidNotification(
                    sound='default',
                    channel_id='urgent_alerts' if is_urgent else 'patient_alerts',
                    priority='high'
                )
            )
            
            # Add APNS (iOS) specific configuration
            apns_config = messaging.APNSConfig(
                payload=messaging.APNSPayload(
                    aps=messaging.Aps(
                        sound='default',
                        badge=1,
                        content_available=True
                    )
                )
            )
            
            # Add data payload
            data = {
                'urgent': 'true' if is_urgent else 'false',
                'type': 'heart_rate_alert' if is_urgent else 'general_alert',
                'click_action': 'FLUTTER_NOTIFICATION_CLICK',
                'project_id': self.config['push'].get('project_id', '')
            }
            
            # Create message
            message = messaging.Message(
                notification=notification,
                android=android_config,
                apns=apns_config,
                data=data,
                token=device_token
            )
            
            # Print debug information
            print(f"Sending Firebase push notification to: {device_token}")
            
            # Send message
            response = messaging.send(message)
            print(f"Firebase response: {response}")
            return True
        except Exception as e:
            print(f"Firebase push notification failed: {e}")
            import traceback
            print(traceback.format_exc())
            return False

    def test_alert_system(self):
        """Test the alert system by sending test alerts through all configured channels"""
        print("Testing alert system...")
        results = {
            "email": False,
            "push": False,
            "overall": False
        }
        
        # Test email
        if self.config['email']['enabled']:
            try:
                email_result = self._send_email(
                    self.default_recipient_email,
                    "This is a test alert from the ICU Monitoring System"
                )
                results["email"] = email_result
                print(f"Email test result: {email_result}")
            except Exception as e:
                print(f"Email test failed: {e}")
        
        # Test push notification
        if self.config['push']['enabled']:
            try:
                push_result = self._send_push(
                    self.default_device_token,
                    "This is a test alert from the ICU Monitoring System"
                )
                results["push"] = push_result
                print(f"Push notification test result: {push_result}")
            except Exception as e:
                print(f"Push notification test failed: {e}")
        
        # Overall result
        results["overall"] = results["email"] or results["push"]
        
        return results










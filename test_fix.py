#!/usr/bin/env python3
"""
Test script to verify the fixes work correctly
"""

import pandas as pd
import numpy as np

def test_abnormal_vitals_detection():
    """Test abnormal vitals detection logic"""
    
    print("Testing Abnormal Vitals Detection...")
    print("=" * 50)
    
    # Define alert thresholds (same as in app.py)
    alert_thresholds = {
        'Heart Rate': [47, 150],
        'O2 Saturation': [90, 100],
        'Systolic BP': [90, 160],
        'Diastolic BP': [60, 90],
        'Temperature (C)': [36.0, 39.5],
        'Respiratory Rate': [8, 30],
        'Glucose': [70, 180]
    }
    
    # Test Case 1: All normal vitals (like in the screenshot)
    print("\n1. Testing Normal Vitals (like in screenshot):")
    normal_vitals = {
        'Heart Rate': 75,
        'O2 Saturation': 98,
        'Systolic BP': 120,
        'Diastolic BP': 80,
        'Temperature (C)': 37.0,
        'Respiratory Rate': 16,
        'Glucose': 100
    }
    
    abnormal_vitals = []
    for vital, value in normal_vitals.items():
        if vital in alert_thresholds:
            min_val, max_val = alert_thresholds[vital]
            if value < min_val:
                abnormal_vitals.append({
                    "Vital Sign": vital,
                    "Current Value": value,
                    "Normal Range": f"{min_val}-{max_val}",
                    "Status": "Low"
                })
            elif value > max_val:
                abnormal_vitals.append({
                    "Vital Sign": vital,
                    "Current Value": value,
                    "Normal Range": f"{min_val}-{max_val}",
                    "Status": "High"
                })
    
    print(f"   Abnormal vitals found: {len(abnormal_vitals)}")
    if abnormal_vitals:
        for vital in abnormal_vitals:
            print(f"   - {vital['Vital Sign']}: {vital['Current Value']} ({vital['Status']})")
    else:
        print("   ✅ All vitals are normal")
    
    # Test prediction logic
    print("\n2. Testing Prediction Logic:")
    abnormal_count = len(abnormal_vitals)
    all_vitals_normal = abnormal_count == 0
    
    if all_vitals_normal:
        base_mortality = 0.025  # 2.5%
        base_los = 2.3
        print(f"   Normal vitals prediction: {base_mortality*100:.1f}% mortality, {base_los:.1f} days LOS")
    elif abnormal_count == 1:
        base_mortality = 0.055  # 5.5%
        base_los = 3.2
        print(f"   1 abnormal prediction: {base_mortality*100:.1f}% mortality, {base_los:.1f} days LOS")
    elif abnormal_count < 3:
        base_mortality = 0.085  # 8.5%
        base_los = 4.1
        print(f"   2 abnormal prediction: {base_mortality*100:.1f}% mortality, {base_los:.1f} days LOS")
    else:
        base_mortality = 0.15 + (abnormal_count - 3) * 0.03
        base_los = 5.5 + (abnormal_count - 3) * 0.5
        print(f"   {abnormal_count} abnormal prediction: {base_mortality*100:.1f}% mortality, {base_los:.1f} days LOS")
    
    # Test Case 2: One abnormal vital
    print("\n3. Testing One Abnormal Vital (HR 46):")
    abnormal_vitals_test2 = []
    test_vitals_2 = normal_vitals.copy()
    test_vitals_2['Heart Rate'] = 46  # Below normal (47-150)
    
    for vital, value in test_vitals_2.items():
        if vital in alert_thresholds:
            min_val, max_val = alert_thresholds[vital]
            if value < min_val:
                abnormal_vitals_test2.append({
                    "Vital Sign": vital,
                    "Current Value": value,
                    "Normal Range": f"{min_val}-{max_val}",
                    "Status": "Low"
                })
            elif value > max_val:
                abnormal_vitals_test2.append({
                    "Vital Sign": vital,
                    "Current Value": value,
                    "Normal Range": f"{min_val}-{max_val}",
                    "Status": "High"
                })
    
    print(f"   Abnormal vitals found: {len(abnormal_vitals_test2)}")
    for vital in abnormal_vitals_test2:
        print(f"   - {vital['Vital Sign']}: {vital['Current Value']} ({vital['Status']})")
    
    abnormal_count_2 = len(abnormal_vitals_test2)
    if abnormal_count_2 == 1:
        base_mortality_2 = 0.055  # 5.5%
        base_los_2 = 3.2
        print(f"   1 abnormal prediction: {base_mortality_2*100:.1f}% mortality, {base_los_2:.1f} days LOS")
    
    print("\n" + "=" * 50)
    print("EXPECTED BEHAVIOR:")
    print("- Normal vitals (like screenshot) should show ~2.5% mortality, ~2.3 days LOS")
    print("- One abnormal vital should show ~5.5% mortality, ~3.2 days LOS")
    print("- System should NOT show 23% mortality for normal vitals!")

if __name__ == "__main__":
    test_abnormal_vitals_detection()

#!/usr/bin/env python3
"""
Test script to verify prediction logic works correctly
"""

import pandas as pd
import numpy as np
from src.models.ensemble_model import EnsembleICUModel
from src.data.data_loader import ICUDataProcessor

def test_prediction_sensitivity():
    """Test that predictions change appropriately with vital sign changes"""

    print("Testing ICU Prediction Sensitivity...")
    print("=" * 50)

    # Initialize model and data processor
    model = EnsembleICUModel()
    data_processor = ICUDataProcessor()

    # Test with actual patient data to see what's happening
    print("\n0. Testing with actual patient data:")
    try:
        # Load some patient data
        data_processor.load_data()
        patient_ids = list(data_processor.patients_data.keys())
        if patient_ids:
            test_patient = patient_ids[0]
            print(f"Testing with patient: {test_patient}")

            # Get patient features
            patient_features = data_processor.get_patient_features(test_patient)
            if patient_features is not None:
                print(f"Patient features shape: {patient_features.shape}")
                print(f"Patient features: {patient_features.iloc[0].to_dict()}")

                # Make prediction
                predictions = model.predict(patient_features)
                print(f"Predictions: {predictions}")
            else:
                print("No patient features available")
        else:
            print("No patients loaded")
    except Exception as e:
        print(f"Error testing with actual data: {e}")
        import traceback
        traceback.print_exc()
    
    # Test Case 1: All normal vitals
    print("\n1. Testing Normal Vitals:")
    normal_vitals = pd.DataFrame({
        'Heart Rate': [75],
        'O2 Saturation': [98],
        'Systolic BP': [120],
        'Diastolic BP': [80],
        'Temperature (C)': [37.0],
        'Respiratory Rate': [16],
        'Glucose': [100]
    })
    
    predictions_normal = model.predict(normal_vitals)
    mortality_normal = predictions_normal['mortality']['ensemble'][0] * 100
    los_normal = predictions_normal['los']['ensemble'][0]
    
    print(f"   Normal vitals: {mortality_normal:.1f}% mortality, {los_normal:.1f} days LOS")
    
    # Test Case 2: One abnormal vital (Heart Rate slightly low)
    print("\n2. Testing One Abnormal Vital (HR 46):")
    one_abnormal = pd.DataFrame({
        'Heart Rate': [46],  # Slightly abnormal
        'O2 Saturation': [98],
        'Systolic BP': [120],
        'Diastolic BP': [80],
        'Temperature (C)': [37.0],
        'Respiratory Rate': [16],
        'Glucose': [100]
    })
    
    predictions_one = model.predict(one_abnormal)
    mortality_one = predictions_one['mortality']['ensemble'][0] * 100
    los_one = predictions_one['los']['ensemble'][0]
    
    print(f"   One abnormal (HR 46): {mortality_one:.1f}% mortality, {los_one:.1f} days LOS")
    
    # Test Case 3: Multiple abnormal vitals
    print("\n3. Testing Multiple Abnormal Vitals:")
    multiple_abnormal = pd.DataFrame({
        'Heart Rate': [45],  # Low
        'O2 Saturation': [88],  # Low
        'Systolic BP': [160],  # High
        'Diastolic BP': [80],
        'Temperature (C)': [38.5],  # High
        'Respiratory Rate': [16],
        'Glucose': [100]
    })
    
    predictions_multiple = model.predict(multiple_abnormal)
    mortality_multiple = predictions_multiple['mortality']['ensemble'][0] * 100
    los_multiple = predictions_multiple['los']['ensemble'][0]
    
    print(f"   Multiple abnormal: {mortality_multiple:.1f}% mortality, {los_multiple:.1f} days LOS")
    
    # Test Case 4: Critical vitals
    print("\n4. Testing Critical Vitals:")
    critical_vitals = pd.DataFrame({
        'Heart Rate': [35],  # Critical
        'O2 Saturation': [82],  # Critical
        'Systolic BP': [180],  # Critical
        'Diastolic BP': [80],
        'Temperature (C)': [40.0],  # Critical
        'Respiratory Rate': [35],  # Critical
        'Glucose': [100]
    })
    
    predictions_critical = model.predict(critical_vitals)
    mortality_critical = predictions_critical['mortality']['ensemble'][0] * 100
    los_critical = predictions_critical['los']['ensemble'][0]
    
    print(f"   Critical vitals: {mortality_critical:.1f}% mortality, {los_critical:.1f} days LOS")
    
    # Verify expected behavior
    print("\n" + "=" * 50)
    print("VERIFICATION:")
    print("=" * 50)
    
    # Check if normal vitals have low predictions
    if mortality_normal <= 5.0 and los_normal <= 3.5:
        print("✅ Normal vitals: PASS (low predictions)")
    else:
        print(f"❌ Normal vitals: FAIL (should be ≤5% mortality, ≤3.5 days LOS)")
    
    # Check if one abnormal has moderate increase
    if mortality_one > mortality_normal and mortality_one <= 15.0:
        print("✅ One abnormal: PASS (moderate increase)")
    else:
        print(f"❌ One abnormal: FAIL (should be moderate increase from normal)")
    
    # Check if multiple abnormal has higher predictions
    if mortality_multiple > mortality_one and los_multiple > los_one:
        print("✅ Multiple abnormal: PASS (higher than one abnormal)")
    else:
        print(f"❌ Multiple abnormal: FAIL (should be higher than one abnormal)")
    
    # Check if critical has highest predictions
    if mortality_critical > mortality_multiple and los_critical > los_multiple:
        print("✅ Critical vitals: PASS (highest predictions)")
    else:
        print(f"❌ Critical vitals: FAIL (should be highest predictions)")
    
    # Check gradual progression
    print(f"\nProgression check:")
    print(f"Normal → One abnormal: {mortality_normal:.1f}% → {mortality_one:.1f}% (Δ{mortality_one-mortality_normal:.1f}%)")
    print(f"One → Multiple: {mortality_one:.1f}% → {mortality_multiple:.1f}% (Δ{mortality_multiple-mortality_one:.1f}%)")
    print(f"Multiple → Critical: {mortality_multiple:.1f}% → {mortality_critical:.1f}% (Δ{mortality_critical-mortality_multiple:.1f}%)")
    
    # Check for reasonable changes (not dramatic jumps)
    if (mortality_one - mortality_normal) < 20.0:  # Less than 20% jump
        print("✅ Gradual change: PASS (no dramatic jumps)")
    else:
        print(f"❌ Gradual change: FAIL (too dramatic jump from normal to one abnormal)")

if __name__ == "__main__":
    test_prediction_sensitivity()

#!/usr/bin/env python3
"""
Test script to verify the improved prediction system provides more nuanced predictions.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from models.ensemble_model import EnsembleICUModel

def test_gradient_predictions():
    """Test that the improved system provides gradient predictions instead of binary jumps."""
    
    print("=" * 60)
    print("TESTING IMPROVED PREDICTION SYSTEM")
    print("=" * 60)
    
    # Initialize the model
    model = EnsembleICUModel()
    
    # Test Case 1: Normal vitals with small variations
    print("\n1. Testing Normal Vitals with Small Variations:")
    print("-" * 50)
    
    test_cases = [
        {
            'name': 'Optimal vitals',
            'data': {
                'heart rate': 75,
                'o2 saturation': 99,
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Normal vitals (slightly elevated HR)',
            'data': {
                'heart rate': 85,  # +10 from optimal
                'o2 saturation': 99,
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Normal vitals (HR at upper normal)',
            'data': {
                'heart rate': 95,  # +20 from optimal
                'o2 saturation': 99,
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Borderline vitals (HR just above normal)',
            'data': {
                'heart rate': 105,  # Just above normal range
                'o2 saturation': 99,
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        }
    ]
    
    results = []
    
    for case in test_cases:
        # Create DataFrame
        df = pd.DataFrame([case['data']])
        
        # Get prediction
        prediction = model.predict(df)
        
        mortality = prediction['mortality']['ensemble'][0] * 100
        los = prediction['los']['ensemble'][0]
        risk_score = prediction.get('risk_stratification', {}).get('overall_score', 0)
        
        results.append({
            'case': case['name'],
            'hr': case['data']['heart rate'],
            'mortality': mortality,
            'los': los,
            'risk_score': risk_score
        })
        
        print(f"{case['name']}:")
        print(f"  Heart Rate: {case['data']['heart rate']} bpm")
        print(f"  Mortality: {mortality:.1f}%")
        print(f"  LOS: {los:.1f} days")
        print(f"  Risk Score: {risk_score:.3f}")
        print()
    
    # Test Case 2: Gradual deterioration
    print("\n2. Testing Gradual Vital Sign Deterioration:")
    print("-" * 50)
    
    deterioration_cases = [
        {
            'name': 'Mild hypoxemia',
            'data': {
                'heart rate': 75,
                'o2 saturation': 93,  # Mild decrease
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Moderate hypoxemia',
            'data': {
                'heart rate': 75,
                'o2 saturation': 90,  # Moderate decrease
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Severe hypoxemia',
            'data': {
                'heart rate': 75,
                'o2 saturation': 85,  # Severe decrease
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        }
    ]
    
    print("Testing O2 Saturation Gradient:")
    for case in deterioration_cases:
        df = pd.DataFrame([case['data']])
        prediction = model.predict(df)
        
        mortality = prediction['mortality']['ensemble'][0] * 100
        risk_score = prediction.get('risk_stratification', {}).get('overall_score', 0)
        risk_level = prediction.get('risk_stratification', {}).get('risk_level', 'Unknown')
        
        print(f"  O2 Sat {case['data']['o2 saturation']}%: Mortality {mortality:.1f}%, Risk Level: {risk_level}")
    
    # Test Case 3: Multiple vital interactions
    print("\n3. Testing Multiple Vital Sign Interactions:")
    print("-" * 50)
    
    interaction_cases = [
        {
            'name': 'Single abnormal vital',
            'data': {
                'heart rate': 110,  # Elevated
                'o2 saturation': 99,
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Two mildly abnormal vitals',
            'data': {
                'heart rate': 110,  # Elevated
                'o2 saturation': 93,  # Mildly low
                'systolic bp': 120,
                'temperature': 36.8,
                'respiratory rate': 16
            }
        },
        {
            'name': 'Multiple concerning vitals',
            'data': {
                'heart rate': 125,  # High
                'o2 saturation': 90,  # Low
                'systolic bp': 160,  # High
                'temperature': 38.5,  # Fever
                'respiratory rate': 25  # High
            }
        }
    ]
    
    for case in interaction_cases:
        df = pd.DataFrame([case['data']])
        prediction = model.predict(df)
        
        mortality = prediction['mortality']['ensemble'][0] * 100
        risk_stratification = prediction.get('risk_stratification', {})
        risk_level = risk_stratification.get('risk_level', 'Unknown')
        primary_drivers = risk_stratification.get('primary_drivers', [])
        
        print(f"{case['name']}:")
        print(f"  Mortality: {mortality:.1f}%")
        print(f"  Risk Level: {risk_level}")
        print(f"  Primary Risk Drivers: {len(primary_drivers)}")
        for driver in primary_drivers[:3]:  # Show top 3
            print(f"    - {driver['vital']}: {driver['value']} (Risk: {driver['risk_score']:.2f})")
        print()
    
    # Summary
    print("\n4. Summary of Improvements:")
    print("-" * 50)
    print("✓ Gradient-based predictions instead of binary normal/abnormal")
    print("✓ Risk stratification with multiple levels")
    print("✓ Detailed vital sign analysis with trends")
    print("✓ Primary risk driver identification")
    print("✓ More nuanced mortality predictions")
    print("✓ Enhanced feature engineering with interactions")
    
    return results

if __name__ == "__main__":
    test_gradient_predictions()

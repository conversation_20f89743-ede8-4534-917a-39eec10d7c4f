import torch
import torch.nn as nn
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, WeightedRandomSampler
import numpy as np
import shap
from sklearn.model_selection import KFold
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer

class BiLSTMModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2, dropout=0.2):
        super().__init__()
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, 
                           batch_first=True, bidirectional=True, dropout=dropout)
        self.fc = nn.Linear(hidden_dim*2, output_dim)
        
    def forward(self, x, return_features=False):
        # x shape: (batch_size, seq_len, input_dim)
        lstm_out, _ = self.lstm(x)  # lstm_out: (batch_size, seq_len, hidden_dim*2)
        
        # Use the last time step for prediction
        features = lstm_out[:, -1, :]
        output = self.fc(features)
        
        if return_features:
            return output, features
        return output
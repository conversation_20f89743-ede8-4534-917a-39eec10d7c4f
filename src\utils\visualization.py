import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
# Remove any import of prediction_model

# Make sure we're not using any functions from the old prediction model
def plot_patient_vitals(patient_data, figsize=(12, 8)):
    """Plot patient vital signs over time"""
    if not patient_data or 'vitals' not in patient_data or not patient_data['vitals']:
        return None
    
    vitals = patient_data['vitals']
    timestamps = [v.get('timestamp', i) for i, v in enumerate(vitals)]
    
    fig, axes = plt.subplots(3, 2, figsize=figsize)
    axes = axes.flatten()
    
    # Plot heart rate
    if any('heart_rate' in v for v in vitals):
        heart_rates = [v.get('heart_rate', None) for v in vitals]
        axes[0].plot(timestamps, heart_rates, 'o-', color='red')
        axes[0].set_title('Heart Rate (bpm)')
        axes[0].set_ylabel('BPM')
    
    # Plot other vitals similarly
    # ...
    
    plt.tight_layout()
    return fig

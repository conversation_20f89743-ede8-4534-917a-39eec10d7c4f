import pandas as pd
import numpy as np
import os
import tempfile
import zipfile
import shutil
from tqdm import tqdm
import glob
from sklearn.impute import SimpleImputer

class ICUDataLoader:
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.patient_dirs = []  # List of patient IDs
        self.temp_dir = None
        self.patients_data = {}  # Store processed patient data
        
        # Dataframes to store all data
        self.pats_df = None
        self.timeseries_df = None
        self.lab_df = None
        self.nc_df = None
        self.patient_id_col = None
    
    def load_from_zip(self, zip_file):
        """Extract patient data from a zip file containing CSV files"""
        try:
            # Create temp directory
            self.temp_dir = tempfile.mkdtemp()
            
            # Extract zip file
            with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
            
            # Check if all_data.csv exists in the extracted files
            all_data_path = os.path.join(self.temp_dir, 'all_data.csv')
            if os.path.exists(all_data_path):
                print(f"Found all_data.csv, loading from this file...")
                return self.load_from_csv(all_data_path)
            
            # If all_data.csv doesn't exist, fall back to original behavior
            subdirs = [d for d in os.listdir(self.temp_dir) 
                      if os.path.isdir(os.path.join(self.temp_dir, d))]
            
            if subdirs:
                # Directory structure - each directory might be a patient
                return self._load_from_patient_directories()
            else:
                # Flat files - all patients in single files
                return self._load_from_flat_files()
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            self._cleanup_temp_dir()
            return False

    def _cleanup_temp_dir(self):
        """Clean up temporary directory"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None

    def load_from_csv(self, csv_file):
        """Load all patient data from a single CSV file"""
        try:
            # Load the CSV file
            print(f"Loading data from {csv_file}...")
            df = pd.read_csv(csv_file, on_bad_lines='skip')
            
            # Check if patientunitstayid column exists
            if 'patientunitstayid' not in df.columns:
                # Try to find a suitable column to use as patient ID
                for col in ['patient_id', 'patientid', 'id', 'subject_id']:
                    if col in df.columns:
                        print(f"Using {col} as patient ID column")
                        df['patientunitstayid'] = df[col]
                        break
                else:
                    # If no suitable column found, create sequential IDs
                    print("No patient ID column found, creating sequential IDs")
                    df['patientunitstayid'] = range(1, len(df) + 1)
            
            self.patient_id_col = 'patientunitstayid'
            
            # Extract unique patient IDs
            self.patient_dirs = df[self.patient_id_col].unique().tolist()
            print(f"Found {len(self.patient_dirs)} unique patients")
            
            # Store the dataframe
            self.pats_df = df
            
            # Process all patients
            self.process_all_patients()
            
            return len(self.patient_dirs) > 0
            
        except Exception as e:
            print(f"Error loading CSV file: {str(e)}")
            return False
            
    def process_all_patients(self):
        """Process all patients in the dataset"""
        if self.pats_df is None:
            print("No patient data loaded")
            return False
            
        print(f"Processing {len(self.patient_dirs)} patients...")
        
        # Process each patient
        for patient_id in tqdm(self.patient_dirs):
            self.process_patient(patient_id)
            
        return True
        
    def process_patient(self, patient_id):
        """Process a single patient's data"""
        # Get patient-specific data
        patient_df = self.pats_df[self.pats_df[self.patient_id_col] == patient_id]

        # Identify time columns
        time_cols = ['observationtime', 'timestamp', 'time', 'datetime', 'charttime']
        timestamp_col = next((col for col in time_cols if col in patient_df.columns), None)

        # Create timeseries_df: remove demographic-only columns
        ignore_cols = ['patientunitstayid', 'age', 'gender', 'ethnicity', 'admittime', 'dischtime']
        timeseries_df = patient_df.drop(columns=[col for col in ignore_cols if col in patient_df.columns], errors='ignore')

        # Make sure timestamp is in the timeseries_df
        if timestamp_col and timestamp_col in patient_df.columns:
            timeseries_df[timestamp_col] = patient_df[timestamp_col]

        # Extract demographics
        demographics = {
            'patientunitstayid': patient_id
        }
        
        # Look for common demographic columns with variations
        demo_mappings = {
            'age': ['age', 'patient_age', 'patientage'],
            'gender': ['gender', 'sex', 'patient_gender', 'patientsex'],
            'weight': ['weight', 'patient_weight', 'patientweight', 'admission_weight', 'admissionweight'],
            'height': ['height', 'patient_height', 'patientheight', 'admissionheight'],
            'bmi': ['bmi', 'patient_bmi', 'patientbmi'],
            'ethnicity': ['ethnicity', 'race', 'patient_ethnicity', 'patientethnicity']
        }
        
        # Extract demographics from dataframe
        for demo_key, possible_cols in demo_mappings.items():
            for col in possible_cols:
                if col in patient_df.columns and not patient_df[col].isna().all():
                    # Get first non-null value
                    value = patient_df[col].dropna().iloc[0] if not patient_df[col].dropna().empty else None
                    demographics[demo_key] = value
                    break
        
        # Look for outcome variables
        # Check for mortality
        mortality_cols = ['mortality', 'death', 'died', 'deceased']
        for col in patient_df.columns:
            if col.lower() in mortality_cols:
                try:
                    value = patient_df[col].dropna().iloc[0] if not patient_df[col].dropna().empty else None
                    if value is not None:
                        if isinstance(value, str):
                            demographics['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                        else:
                            demographics['mortality'] = 1 if value > 0 else 0
                        print(f"Found mortality: {demographics['mortality']} from column {col}")
                        break
                except (IndexError, ValueError):
                    continue
        
        # Check for length of stay
        los_cols = ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay', 'stay_length', 'stay_duration']
        for col in patient_df.columns:
            if col.lower() in los_cols or col in los_cols:
                try:
                    value = patient_df[col].dropna().iloc[0] if not patient_df[col].dropna().empty else None
                    if value is not None:
                        if col.lower() == 'lengthofstay' or col == 'Lengthof stay':
                            demographics['lengthofstay'] = float(value)
                            print(f"Found lengthofstay: {demographics['lengthofstay']} from column {col}")
                        else:
                            demographics['los'] = float(value)
                            print(f"Found los: {demographics['los']} from column {col}")
                        break
                except (IndexError, ValueError):
                    continue

        # Store patient data
        self.patients_data[patient_id] = {
            'timeseries_df': timeseries_df,
            'demographics': demographics,
            'feature_names': list(timeseries_df.columns),
            'demographics_df': patient_df,
            'lab_df': None,
            'nc_df': None,
            'processed': True
        }

        return self.patients_data[patient_id]

        
    def process_patient_data(self, patient_id):
        """Process data for a specific patient"""
        try:
            # Check if already processed
            if hasattr(self, 'patients_data') and patient_id in self.patients_data:
                return self.patients_data[patient_id]
            
            # Get patient directory
            if patient_id not in self.patient_dirs:
                print(f"Patient {patient_id} not found in dataset")
                return None
            
            # Initialize result dictionary
            result = {}
            
            # Get the patient directory path
            patient_dir = os.path.join(self.data_dir, str(patient_id))
            if not os.path.exists(patient_dir):
                # Try to find the patient directory by searching
                for root, dirs, files in os.walk(self.data_dir):
                    for dir_name in dirs:
                        if dir_name == str(patient_id):
                            patient_dir = os.path.join(root, dir_name)
                            break
                    if os.path.exists(patient_dir):
                        break
            
            # If we still can't find the directory, try using the patient_dirs mapping
            if not os.path.exists(patient_dir) and hasattr(self, 'patient_dirs') and patient_id in self.patient_dirs:
                patient_dir = self.patient_dirs[patient_id]
            
            # Process demographics
            demo_file = os.path.join(patient_dir, 'demographics.csv')
            if os.path.exists(demo_file):
                try:
                    demo_df = pd.read_csv(demo_file)
                    result['demographics_df'] = demo_df
                    
                    # Extract key demographics into a dictionary
                    demographics = {}
                    demographics['patientunitstayid'] = patient_id
                    
                    # Try to extract common demographic fields
                    for field in ['age', 'gender', 'ethnicity', 'height', 'weight', 'bmi']:
                        if field in demo_df.columns:
                            try:
                                demographics[field] = demo_df[field].iloc[0]
                            except (IndexError, ValueError):
                                demographics[field] = None
                    
                    # Check for mortality and LOS
                    for field in ['mortality', 'death', 'died', 'deceased']:
                        if field in demo_df.columns:
                            try:
                                value = demo_df[field].iloc[0]
                                if isinstance(value, str):
                                    demographics['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                                else:
                                    demographics['mortality'] = 1 if value > 0 else 0
                                print(f"Found mortality: {demographics['mortality']} from column {field}")
                                break
                            except (IndexError, ValueError):
                                pass
                    
                    # Check for length of stay
                    for field in ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay']:
                        if field in demo_df.columns:
                            try:
                                demographics[field.lower()] = float(demo_df[field].iloc[0])
                                print(f"Found {field}: {demographics[field.lower()]} from demographics")
                                break
                            except (IndexError, ValueError):
                                pass
                    
                    result['demographics'] = demographics
                except Exception as e:
                    print(f"Error processing demographics for patient {patient_id}: {e}")
                    result['demographics'] = {'patientunitstayid': patient_id}
                    result['demographics_df'] = None
            else:
                # Try to find any CSV file that might contain demographics
                for root, dirs, files in os.walk(patient_dir):
                    for file in files:
                        if file.endswith('.csv'):
                            try:
                                df = pd.read_csv(os.path.join(root, file))
                                
                                # Check if this file has demographic or outcome columns
                                demo_cols = ['age', 'gender', 'ethnicity', 'height', 'weight', 'bmi']
                                outcome_cols = ['mortality', 'death', 'died', 'deceased', 'lengthofstay', 'Lengthof stay', 'los', 'length_of_stay']
                                
                                # Check if any of these columns exist in the file
                                if any(col in df.columns for col in demo_cols + outcome_cols):
                                    print(f"Found potential demographics file: {file}")
                                    result['demographics_df'] = df
                                    
                                    # Extract demographics
                                    demographics = {'patientunitstayid': patient_id}
                                    
                                    # Extract demographic fields
                                    for field in demo_cols:
                                        if field in df.columns:
                                            try:
                                                demographics[field] = df[field].iloc[0]
                                            except (IndexError, ValueError):
                                                demographics[field] = None
                                    
                                    # Extract outcome fields
                                    for field in ['mortality', 'death', 'died', 'deceased']:
                                        if field in df.columns:
                                            try:
                                                value = df[field].iloc[0]
                                                if isinstance(value, str):
                                                    demographics['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                                                else:
                                                    demographics['mortality'] = 1 if value > 0 else 0
                                                print(f"Found mortality: {demographics['mortality']} from column {field}")
                                                break
                                            except (IndexError, ValueError):
                                                pass
                                    
                                    # Extract length of stay
                                    for field in ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay']:
                                        if field in df.columns:
                                            try:
                                                demographics[field.lower()] = float(df[field].iloc[0])
                                                print(f"Found {field}: {demographics[field.lower()]} from file {file}")
                                                break
                                            except (IndexError, ValueError):
                                                pass
                                    
                                    result['demographics'] = demographics
                                    break
                            except Exception as e:
                                print(f"Error processing file {file} for patient {patient_id}: {e}")
                                continue
            
            # Process timeseries data
            ts_file = os.path.join(patient_dir, 'timeseries.csv')
            if os.path.exists(ts_file):
                try:
                    ts_df = pd.read_csv(ts_file)
                    result['timeseries_df'] = ts_df
                    
                    # Check for outcome variables in timeseries data
                    if 'demographics' not in result:
                        result['demographics'] = {'patientunitstayid': patient_id}
                    
                    # Check for mortality
                    for field in ['mortality', 'death', 'died', 'deceased']:
                        if field in ts_df.columns:
                            try:
                                value = ts_df[field].iloc[-1]  # Use the last value
                                if isinstance(value, str):
                                    result['demographics']['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                                else:
                                    result['demographics']['mortality'] = 1 if value > 0 else 0
                                print(f"Found mortality: {result['demographics']['mortality']} from timeseries column {field}")
                                break
                            except (IndexError, ValueError):
                                pass
                    
                    # Check for length of stay
                    for field in ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay']:
                        if field in ts_df.columns:
                            try:
                                result['demographics'][field.lower()] = float(ts_df[field].iloc[-1])  # Use the last value
                                print(f"Found {field}: {result['demographics'][field.lower()]} from timeseries")
                                break
                            except (IndexError, ValueError):
                                pass
                except Exception as e:
                    print(f"Error processing timeseries for patient {patient_id}: {e}")
                    result['timeseries_df'] = None
            else:
                # Try to find any CSV file that might contain timeseries data
                for root, dirs, files in os.walk(patient_dir):
                    for file in files:
                        if file.endswith('.csv') and file != 'demographics.csv':
                            try:
                                df = pd.read_csv(os.path.join(root, file))
                                
                                # Check if this file has vital sign columns
                                vital_cols = ['heart rate', 'hr', 'pulse', 'respiratory rate', 'rr', 'o2 saturation', 'spo2', 'temperature', 'temp']
                                
                                # Check if any of these columns exist in the file (case insensitive)
                                if any(any(vital.lower() in col.lower() for vital in vital_cols) for col in df.columns):
                                    print(f"Found potential timeseries file: {file}")
                                    result['timeseries_df'] = df
                                    
                                    # Check for outcome variables
                                    if 'demographics' not in result:
                                        result['demographics'] = {'patientunitstayid': patient_id}
                                    
                                    # Check for mortality
                                    for field in ['mortality', 'death', 'died', 'deceased']:
                                        if field in df.columns:
                                            try:
                                                value = df[field].iloc[-1]  # Use the last value
                                                if isinstance(value, str):
                                                    result['demographics']['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                                                else:
                                                    result['demographics']['mortality'] = 1 if value > 0 else 0
                                                print(f"Found mortality: {result['demographics']['mortality']} from file {file}")
                                                break
                                            except (IndexError, ValueError):
                                                pass
                                    
                                    # Check for length of stay
                                    for field in ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay']:
                                        if field in df.columns:
                                            try:
                                                result['demographics'][field.lower()] = float(df[field].iloc[-1])  # Use the last value
                                                print(f"Found {field}: {result['demographics'][field.lower()]} from file {file}")
                                                break
                                            except (IndexError, ValueError):
                                                pass
                                    
                                    break
                            except Exception as e:
                                print(f"Error processing file {file} for patient {patient_id}: {e}")
                                continue
            
            # If we still don't have outcome variables, add default values
            if 'demographics' in result:
                if 'mortality' not in result['demographics']:
                    result['demographics']['mortality'] = 0  # Default: survived
                    print(f"Added default mortality=0 for patient {patient_id}")
                
                if 'los' not in result['demographics'] and 'lengthofstay' not in result['demographics']:
                    result['demographics']['los'] = 5.0  # Default: 5 days
                    print(f"Added default los=5.0 for patient {patient_id}")
            
            # Store processed data
            if hasattr(self, 'patients_data'):
                self.patients_data[patient_id] = result
            
            return result
        except Exception as e:
            print(f"Error processing patient data: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def get_patient_features(self, patient_id):
        """
        Extract features for a patient to be used for prediction.
        
        Returns a pandas DataFrame with features in the format expected by the model.
        """
        if patient_id not in self.patient_dirs:
            print(f"Patient {patient_id} not found in dataset")
            return None
        
        # Get patient data
        patient_data = self.process_patient_data(patient_id)
        if not patient_data:
            # Try to get directly from patients_data if process_patient_data failed
            if hasattr(self, 'patients_data') and patient_id in self.patients_data:
                patient_data = self.patients_data[patient_id]
            else:
                print(f"No processed data available for patient {patient_id}")
                return None
        
        # Extract features from patient data
        features = {}
        
        # Add patient ID - use both patientunitstayid and patient_id for compatibility
        features['patientunitstayid'] = patient_id
        features['patient_id'] = patient_id
        
        # Add demographic features
        if 'demographics' in patient_data and patient_data['demographics']:
            demo = patient_data['demographics']
            for key, value in demo.items():
                if key != 'demographics_df' and key not in ['patientunitstayid', 'patient_id']:  # Skip the dataframe and IDs
                    # Convert to numeric if possible
                    try:
                        # Clean the value if it's a string
                        if isinstance(value, str):
                            # Remove any non-numeric characters for numeric fields
                            if key.lower() in ['age', 'weight', 'height', 'bmi']:
                                value = ''.join(c for c in value if c.isdigit() or c == '.')
                        features[key] = float(value) if value else 0.0
                    except (ValueError, TypeError):
                        features[key] = str(value) if value else ""
        
        # Add medical history if available
        if 'medical_history' in patient_data and patient_data['medical_history']:
            med_history = patient_data['medical_history']
            for key, value in med_history.items():
                features[key] = 1 if value else 0
        
        # Add enhanced vital sign features with trends and interactions
        if 'timeseries_df' in patient_data and patient_data['timeseries_df'] is not None:
            ts_df = patient_data['timeseries_df']

            # Get vital sign columns
            vital_cols = [col for col in ts_df.columns
                         if col not in ['time', 'timestamp', 'datetime', 'date', 'observationtime', 'time_index']]

            for col in vital_cols:
                if not ts_df[col].empty:
                    try:
                        # Clean the data - remove non-numeric values
                        clean_values = pd.to_numeric(ts_df[col], errors='coerce').dropna()

                        if len(clean_values) > 0:
                            # Current value (latest)
                            features[f'{col}_current'] = float(clean_values.iloc[-1])

                            # Statistical features
                            features[f'{col}_mean'] = float(clean_values.mean())
                            features[f'{col}_std'] = float(clean_values.std()) if len(clean_values) > 1 else 0.0
                            features[f'{col}_min'] = float(clean_values.min())
                            features[f'{col}_max'] = float(clean_values.max())
                            features[f'{col}_range'] = float(clean_values.max() - clean_values.min())

                            # Trend features (if we have multiple values)
                            if len(clean_values) >= 2:
                                # Calculate trend (slope)
                                import numpy as np
                                x = np.arange(len(clean_values))
                                slope = np.polyfit(x, clean_values, 1)[0]
                                features[f'{col}_trend'] = float(slope)

                                # Variability (coefficient of variation)
                                cv = clean_values.std() / clean_values.mean() if clean_values.mean() != 0 else 0
                                features[f'{col}_variability'] = float(cv)

                                # Recent change (last value vs previous)
                                recent_change = clean_values.iloc[-1] - clean_values.iloc[-2]
                                features[f'{col}_recent_change'] = float(recent_change)

                                # Percentage change from first to last
                                if clean_values.iloc[0] != 0:
                                    pct_change = (clean_values.iloc[-1] - clean_values.iloc[0]) / clean_values.iloc[0] * 100
                                    features[f'{col}_pct_change'] = float(pct_change)
                                else:
                                    features[f'{col}_pct_change'] = 0.0
                            else:
                                # Single value - set trend features to 0
                                features[f'{col}_trend'] = 0.0
                                features[f'{col}_variability'] = 0.0
                                features[f'{col}_recent_change'] = 0.0
                                features[f'{col}_pct_change'] = 0.0

                            # Risk score based on deviation from normal ranges
                            risk_score = self._calculate_vital_risk_score(col, clean_values.iloc[-1])
                            features[f'{col}_risk_score'] = float(risk_score)

                            # Debug print to verify value accuracy
                            print(f"Extracted vital '{col}': current={features[f'{col}_current']}, trend={features[f'{col}_trend']:.3f}, risk={features[f'{col}_risk_score']:.3f}")

                        else:
                            # No valid values - set all features to 0
                            self._set_zero_vital_features(features, col)
                    except (ValueError, TypeError, IndexError) as e:
                        print(f"Error processing vital {col}: {e}")
                        self._set_zero_vital_features(features, col)
                else:
                    # Empty column - set all features to 0
                    self._set_zero_vital_features(features, col)

            # Add interaction features between key vitals
            self._add_vital_interactions(features, vital_cols)

        
        # IMPORTANT: Check for outcome variables directly in the timeseries dataframe
        # This is critical for training data
        for outcome_col in ['mortality', 'Mortality', 'death', 'Death', 'died', 'Died']:
            if outcome_col in ts_df.columns:
                try:
                    value = ts_df[outcome_col].iloc[-1]
                    if isinstance(value, str):
                        features['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                    else:
                        features['mortality'] = 1 if value > 0 else 0
                    print(f"Found mortality outcome: {features['mortality']} from column {outcome_col}")
                    break
                except (IndexError, ValueError, TypeError):
                    continue
        
        # Check for length of stay columns with various spellings
        for los_col in ['lengthofstay', 'Lengthof stay', 'length_of_stay', 'LOS', 'los', 'stay_length']:
            if los_col in ts_df.columns:
                try:
                    value = ts_df[los_col].iloc[-1]
                    if pd.notna(value):
                        # Try to convert to float
                        if isinstance(value, str):
                            # Extract numeric part
                            value = ''.join(c for c in value if c.isdigit() or c == '.')
                        features['lengthofstay'] = float(value)
                        print(f"Found length of stay: {features['lengthofstay']} from column {los_col}")
                        break
                except (IndexError, ValueError, TypeError):
                    continue
        
        # Add outcome variables if available in the patient data
        # This is useful for training data
        if 'demographics_df' in patient_data and patient_data['demographics_df'] is not None:
            df = patient_data['demographics_df']
            
            # Check for mortality
            mortality_cols = ['mortality', 'death', 'died', 'deceased']
            for col in df.columns:
                if col.lower() in mortality_cols:
                    try:
                        # Get the first non-null value
                        value = df[col].dropna().iloc[0] if not df[col].dropna().empty else None
                        if value is not None:
                            # Convert to binary (0 or 1)
                            if isinstance(value, str):
                                features['mortality'] = 1 if value.lower() in ['yes', 'true', '1', 'y', 't'] else 0
                            else:
                                features['mortality'] = 1 if value > 0 else 0
                        break
                    except (IndexError, ValueError):
                        continue
            
            # Check for length of stay (in hours or days)
            los_cols = ['lengthofstay', 'Lengthof stay', 'los', 'length_of_stay', 'stay_length', 'stay_duration']
            for col in df.columns:
                if col.lower() in los_cols:
                    try:
                        # Get the first non-null value
                        value = df[col].dropna().iloc[0] if not df[col].dropna().empty else None
                        if value is not None:
                            # If column is 'lengthofstay', assume it's in hours and store as is
                            if col.lower() == 'lengthofstay' or col == 'Lengthof stay':
                                features['lengthofstay'] = float(value)
                                print(f"Found lengthofstay in demographics: {features['lengthofstay']} from column {col}")
                            else:
                                # Otherwise, assume it's already in days
                                features['los'] = float(value)
                                print(f"Found los in demographics: {features['los']} from column {col}")
                        break
                    except (IndexError, ValueError):
                        continue
        
        # If we have lengthofstay but not los, convert it
        if 'lengthofstay' in features and 'los' not in features:
            try:
                features['los'] = float(features['lengthofstay']) / 24.0
                print(f"Converted lengthofstay {features['lengthofstay']} to los {features['los']}")
            except (ValueError, TypeError):
                pass
        
        # If we don't have mortality, add a default value of 0 for training purposes
        # This is a fallback to ensure we have at least one outcome variable
        if 'mortality' not in features:
            print("No mortality data found, adding default value of 0")
            features['mortality'] = 0
        
        # Convert to DataFrame
        features_df = pd.DataFrame([features])
        print(f"Extracted {len(features_df.columns)} features for patient {patient_id}")
        print(f"Features: {features}")
        
        return features_df

    def predict_patient_outcomes(self, patient_id):
        """
        This method is kept for backward compatibility.
        It now returns a formatted dictionary with predictions
        that matches the expected format in the app.
        """
        # Get patient features
        patient_features = self.get_patient_features(patient_id)
        
        if patient_features is None:
            return None
        
        # For now, return a simple rule-based prediction
        # In a real application, this would use the ensemble model
        predictions = {
            'mortality_rate': 10.0,  # in percent
            'length_of_stay': 3.0,   # in days
            'risk_factor': 15.0      # in percent
        }
        
        # Apply some simple rules based on age and vitals
        demographics = self.process_patient_data(patient_id).get('demographics', {})
        vitals = self.process_patient_data(patient_id).get('vitals', [])
        
        if demographics and 'age' in demographics:
            age = demographics['age']
            # Increase mortality risk with age
            if age > 70:
                predictions['mortality_rate'] += 10.0
                predictions['length_of_stay'] += 2.0
            elif age > 60:
                predictions['mortality_rate'] += 5.0
                predictions['length_of_stay'] += 1.0
        
        if vitals and len(vitals) > 0:
            latest_vitals = vitals[-1]
            
            # Check for abnormal vital signs
            if 'heart_rate' in latest_vitals and (latest_vitals['heart_rate'] > 100 or latest_vitals['heart_rate'] < 50):
                predictions['mortality_rate'] += 5.0
                predictions['length_of_stay'] += 1.0
            
            if 'sbp' in latest_vitals and (latest_vitals['sbp'] > 180 or latest_vitals['sbp'] < 90):
                predictions['mortality_rate'] += 7.0
                predictions['length_of_stay'] += 1.5
        
        # Calculate risk factor based on mortality and LOS
        predictions['risk_factor'] = (predictions['mortality_rate'] + min(predictions['length_of_stay'] * 5, 50)) / 2
        
        # Ensure values are within reasonable ranges
        predictions['mortality_rate'] = min(max(predictions['mortality_rate'], 0.0), 100.0)
        predictions['length_of_stay'] = max(predictions['length_of_stay'], 1.0)  # Minimum 1 day
        predictions['risk_factor'] = min(max(predictions['risk_factor'], 0.0), 100.0)
        
        return predictions

    def store_prediction_history(self, patient_id, predictions, timestamp=None):
        """Store prediction history for a patient"""
        if timestamp is None:
            from datetime import datetime
            timestamp = datetime.now()
        
        if not hasattr(self, 'prediction_history'):
            self.prediction_history = {}
        
        if patient_id not in self.prediction_history:
            self.prediction_history[patient_id] = []
    
        # Store prediction with timestamp
        # Make sure to extract the actual values from numpy arrays if present
        mortality_value = None
        if 'mortality' in predictions and 'ensemble' in predictions['mortality']:
            mortality_value = float(predictions['mortality']['ensemble'][0])
    
        los_value = None
        if 'los' in predictions and 'ensemble' in predictions['los']:
            los_value = float(predictions['los']['ensemble'][0])
    
        self.prediction_history[patient_id].append({
            'timestamp': timestamp,
            'mortality': mortality_value,
            'los': los_value,
            'abnormal_vitals_count': predictions.get('abnormal_vitals_count', 0)
        })
    
        # Keep only the last 10 predictions
        if len(self.prediction_history[patient_id]) > 10:
            self.prediction_history[patient_id] = self.prediction_history[patient_id][-10:]
    
        return self.prediction_history[patient_id]

    def get_prediction_history(self, patient_id):
        """Get prediction history for a patient"""
        if not hasattr(self, 'prediction_history') or patient_id not in self.prediction_history:
            return []
        
        return self.prediction_history[patient_id]

    def update_patient_vitals(self, patient_id, new_vitals):
        """Update patient vitals with new measurements"""
        if patient_id not in self.patients_data:
            print(f"Patient {patient_id} not found")
            return False
        
        patient_data = self.patients_data[patient_id]
        
        if 'timeseries_df' not in patient_data or patient_data['timeseries_df'] is None:
            # Create new timeseries dataframe if it doesn't exist
            from datetime import datetime
            patient_data['timeseries_df'] = pd.DataFrame({
                'timestamp': [datetime.now()]
            })
        
        # Get the existing dataframe
        df = patient_data['timeseries_df']
        
        # Find timestamp column
        timestamp_cols = ['timestamp', 'datetime', 'time', 'observationtime', 'time_index']
        from datetime import datetime

       #  Always use real timestamp
        timestamp_col = 'timestamp'

      # Create new row with current time
        new_row = {timestamp_col: datetime.now()}

        
        # Add vital signs to the new row - store exact values without any modification
        for vital, value in new_vitals.items():
            new_row[vital] = value  # Store the exact value without conversion
        
        # Append the new row to the dataframe
        new_df = pd.DataFrame([new_row])
        patient_data['timeseries_df'] = pd.concat([df, new_df], ignore_index=True)
        
        print(f"Updated vitals for patient {patient_id}: {new_vitals}")
        print(f"New dataframe shape: {patient_data['timeseries_df'].shape}")
        
        # Update last_updated timestamp
        patient_data['last_updated'] = datetime.now()
        
        # Recalculate features after updating vitals
        self._update_patient_features(patient_id)
        
        return True
    
    def _update_patient_features(self, patient_id):
        """Update patient features after vitals change"""
        if patient_id not in self.patients_data:
            return False
        
        patient_data = self.patients_data[patient_id]
        
        # Extract latest vitals
        if 'timeseries_df' in patient_data and patient_data['timeseries_df'] is not None:
            df = patient_data['timeseries_df']
            
            # Create or update features dictionary
            if 'features' not in patient_data:
                patient_data['features'] = {}
            
            # Update with latest values for each column
            for col in df.columns:
                if col in ['time_index', 'timestamp', 'datetime', 'time', 'observationtime']:
                    continue
                
                latest_value = df[col].dropna().iloc[-1] if not df[col].dropna().empty else None
                if latest_value is not None:
                    patient_data['features'][col] = latest_value
            
            # Add demographic features
            if 'demographics' in patient_data and patient_data['demographics']:
                for key, value in patient_data['demographics'].items():
                    if key not in ['patientunitstayid', 'patient_id']:
                        patient_data['features'][key] = value
            
            print(f"Updated features for patient {patient_id}")
            return True
        
        return False

    def _calculate_vital_risk_score(self, vital_name, value):
        """Calculate risk score for a single vital sign value"""
        # Define normal ranges for risk scoring
        normal_ranges = {
            'heart rate': {'optimal': [70, 85], 'normal': [60, 100], 'concerning': [50, 120], 'critical': [40, 150]},
            'hr': {'optimal': [70, 85], 'normal': [60, 100], 'concerning': [50, 120], 'critical': [40, 150]},
            'o2 saturation': {'optimal': [98, 100], 'normal': [95, 100], 'concerning': [90, 94], 'critical': [80, 89]},
            'spo2': {'optimal': [98, 100], 'normal': [95, 100], 'concerning': [90, 94], 'critical': [80, 89]},
            'map': {'optimal': [75, 95], 'normal': [65, 105], 'concerning': [55, 115], 'critical': [45, 130]},
            'temperature': {'optimal': [36.5, 37.0], 'normal': [36.0, 37.5], 'concerning': [35.5, 38.5], 'critical': [34.0, 40.0]},
            'systolic bp': {'optimal': [110, 130], 'normal': [100, 140], 'concerning': [90, 160], 'critical': [70, 180]},
            'diastolic bp': {'optimal': [70, 80], 'normal': [60, 90], 'concerning': [50, 100], 'critical': [40, 110]},
            'respiratory rate': {'optimal': [14, 18], 'normal': [12, 20], 'concerning': [10, 25], 'critical': [8, 35]},
            'glucose': {'optimal': [90, 120], 'normal': [80, 140], 'concerning': [70, 180], 'critical': [50, 250]}
        }

        vital_lower = vital_name.lower()
        ranges = None

        # Find matching range
        for key, range_dict in normal_ranges.items():
            if key in vital_lower:
                ranges = range_dict
                break

        if ranges is None:
            return 0.0  # Unknown vital, assume normal

        # Calculate risk score based on ranges
        optimal_min, optimal_max = ranges['optimal']
        normal_min, normal_max = ranges['normal']
        concerning_min, concerning_max = ranges['concerning']
        critical_min, critical_max = ranges['critical']

        if optimal_min <= value <= optimal_max:
            return 0.0  # Optimal
        elif normal_min <= value <= normal_max:
            return 0.1  # Normal but not optimal
        elif concerning_min <= value <= concerning_max:
            return 0.5  # Concerning
        elif critical_min <= value <= critical_max:
            return 0.8  # Critical
        else:
            return 1.0  # Beyond critical

    def _set_zero_vital_features(self, features, col):
        """Set all vital-related features to zero for a given column"""
        features[f'{col}_current'] = 0.0
        features[f'{col}_mean'] = 0.0
        features[f'{col}_std'] = 0.0
        features[f'{col}_min'] = 0.0
        features[f'{col}_max'] = 0.0
        features[f'{col}_range'] = 0.0
        features[f'{col}_trend'] = 0.0
        features[f'{col}_variability'] = 0.0
        features[f'{col}_recent_change'] = 0.0
        features[f'{col}_pct_change'] = 0.0
        features[f'{col}_risk_score'] = 0.0

    def _add_vital_interactions(self, features, vital_cols):
        """Add interaction features between key vitals"""
        # Key vital interactions that are medically relevant
        interactions = [
            ('heart rate', 'systolic bp', 'hr_bp_ratio'),
            ('o2 saturation', 'respiratory rate', 'o2_resp_ratio'),
            ('systolic bp', 'diastolic bp', 'pulse_pressure'),
            ('heart rate', 'o2 saturation', 'hr_o2_product'),
            ('temperature', 'heart rate', 'temp_hr_ratio')
        ]

        for vital1, vital2, feature_name in interactions:
            # Find matching columns
            col1 = None
            col2 = None

            for col in vital_cols:
                col_lower = col.lower()
                if vital1 in col_lower and col1 is None:
                    col1 = col
                if vital2 in col_lower and col2 is None:
                    col2 = col

            # Calculate interaction if both vitals are present
            if col1 and col2:
                val1 = features.get(f'{col1}_current', 0)
                val2 = features.get(f'{col2}_current', 0)

                if val1 != 0 and val2 != 0:
                    if 'ratio' in feature_name:
                        features[feature_name] = float(val1 / val2)
                    elif 'product' in feature_name:
                        features[feature_name] = float(val1 * val2)
                    elif 'pressure' in feature_name:  # pulse pressure = systolic - diastolic
                        features[feature_name] = float(val1 - val2)
                else:
                    features[feature_name] = 0.0
            else:
                features[feature_name] = 0.0





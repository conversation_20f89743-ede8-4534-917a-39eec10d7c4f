import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import streamlit as st
import tempfile
import shutil
import zipfile
import datetime
import joblib
from src.models.ensemble_model import EnsembleICUModel
from src.data.data_loader import ICUDataLoader
from src.alerts.alert_system import AlertSystem

st.set_page_config(page_title="EICU Patient Data Visualization", layout="wide")

@st.cache_resource
def load_data_processor():
    try:
        processor = ICUDataLoader("data/")
        # Initialize empty patients_data if it doesn't exist
        if not hasattr(processor, 'patients_data'):
            processor.patients_data = {}
        # Initialize empty patient_dirs if it doesn't exist
        if not hasattr(processor, 'patient_dirs'):
            processor.patient_dirs = []
        return processor
    except Exception as e:
        print(f"Error initializing data processor: {str(e)}")
        return None

def plot_patient_vitals(patient_id, data_processor, alert_system, alert_enabled, selected_vitals=None):
    """Plot patient vitals with alerts"""
    try:
        patient_data = data_processor.process_patient_data(patient_id)
        
        if patient_data is None:
            st.error("Failed to process patient data. Please make sure you've loaded data correctly.")
            return None, {}, False
        
        if 'timeseries_df' not in patient_data or patient_data['timeseries_df'] is None:
            st.info("No vital signs data available for this patient.")
            return None, {}, False
        
        df = patient_data['timeseries_df']
        
        # Make sure df is a DataFrame
        if not isinstance(df, pd.DataFrame):
            st.error("Timeseries data is not in the expected format")
            return None, {}, False
        
        # Check if this is a manually created patient
        is_manual_patient = patient_data.get('is_synthetic', False)
        
        # Get available vital columns
        all_vital_columns = [col for col in df.columns if col not in ['time_index', 'timestamp', 'datetime', 'time', 'observationtime']]

        # Use selected_vitals if provided, otherwise use all available vitals
        if selected_vitals is not None:
            vital_columns = [col for col in selected_vitals if col in all_vital_columns]
        else:
            vital_columns = all_vital_columns
        
        # Define alert thresholds for common vital signs
        alert_thresholds = {
            'Heart Rate': [47, 150],
            'HR': [47, 150],
            'Pulse': [47, 150],
            'O2 Saturation': [90, 100],
            'SPO2': [90, 100],
            'Oxygen': [90, 100],
            'MAP (mmHg)': [50, 180],
            'MAP': [50, 180],
            'Temperature (C)': [36.0, 39.5],
            'Temperature': [36.0, 39.5],
            'Temp': [36.0, 39.5],
            'Glucose': [70, 180],
            'GCS': [3, 15],
            'Respiratory Rate': [8, 30],
            'RR': [8, 30],
            'Resp': [8, 30],
            'Systolic BP': [90, 160],
            'SBP': [90, 160],
            'Diastolic BP': [60, 90],
            'DBP': [60, 90],
            'Blood Pressure': [90, 160]
        }
        
        # Create a dashboard with current values
        st.subheader("Current Vital Signs")
        
        # Create columns for vital signs display
        cols = st.columns(3)
        col_index = 0
        
        # Track abnormal vitals
        abnormal_vitals = []
        
        # Display each vital sign with its current value
        for vital in vital_columns:
            try:
                # Get latest value
                latest_value = df[vital].dropna().iloc[-1] if not df[vital].dropna().empty else None
                
                if latest_value is not None:
                    # Find matching threshold (case insensitive)
                    threshold = None
                    for threshold_name, threshold_values in alert_thresholds.items():
                        if threshold_name.lower() in vital.lower() or vital.lower() in threshold_name.lower():
                            threshold = threshold_values
                            break
                    
                    # Format the display
                    if threshold:
                        min_val, max_val = threshold
                        
                        # Check if value is outside normal range
                        if latest_value < min_val:
                            # Below normal range - show in red
                            cols[col_index].metric(
                                vital, 
                                f"{latest_value}", 
                                f"⚠️ Below normal ({min_val}-{max_val})",
                                delta_color="inverse"
                            )
                            # Add to abnormal vitals
                            abnormal_vitals.append({
                                "Vital Sign": vital,
                                "Current Value": latest_value,
                                "Normal Range": f"{min_val}-{max_val}",
                                "Status": "Low"
                            })
                        elif latest_value > max_val:
                            # Above normal range - show in red
                            cols[col_index].metric(
                                vital, 
                                f"{latest_value}", 
                                f"⚠️ Above normal ({min_val}-{max_val})",
                                delta_color="inverse"
                            )
                            # Add to abnormal vitals
                            abnormal_vitals.append({
                                "Vital Sign": vital,
                                "Current Value": latest_value,
                                "Normal Range": f"{min_val}-{max_val}",
                                "Status": "High"
                            })
                        else:
                            # Within normal range - show in green
                            cols[col_index].metric(
                                vital, 
                                f"{latest_value}", 
                                f"Normal ({min_val}-{max_val})",
                                delta_color="normal"
                            )
                    else:
                        # No threshold found, just show the value
                        cols[col_index].metric(vital, f"{latest_value}")
                else:
                    # No value available
                    cols[col_index].metric(vital, "No data")
            except Exception as e:
                cols[col_index].error(f"Error: {str(e)}")
            
            # Move to next column
            col_index = (col_index + 1) % 3
        
        # Display abnormal vitals table if any were found
        if abnormal_vitals:
            st.subheader("⚠️ Abnormal Vital Signs")
            
            # Create DataFrame for better display
            abnormal_df = pd.DataFrame(abnormal_vitals)
            
            # Apply custom formatting
            st.dataframe(abnormal_df, use_container_width=True)
            
            # Send alerts if enabled
            if alert_enabled and alert_system and patient_id:
                # Prepare vitals for alert system
                vitals_for_alert = {row['Vital Sign']: row['Current Value'] for row in abnormal_vitals}
                
                # Add a button to manually send alerts
                if st.button("Send Alert to Healthcare Providers"):
                    try:
                        # Create a detailed alert message
                        alert_message = f"URGENT ALERT for Patient {patient_id}:\n\n"
                        for vital in abnormal_vitals:
                            alert_message += f"- {vital['Vital Sign']} is {vital['Status'].upper()}: {vital['Current Value']} (normal range: {vital['Normal Range']})\n"
                        
                        # Add patient demographics if available
                        if 'demographics' in patient_data and patient_data['demographics']:
                            alert_message += "\nPatient Information:\n"
                            for key, value in patient_data['demographics'].items():
                                if key not in ['patientunitstayid', 'patient_id']:
                                    alert_message += f"- {key}: {value}\n"
                        
                        # Add timestamp
                        import datetime
                        alert_message += f"\nTimestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        
                        # Send direct email alert
                        subject = f"URGENT: Patient {patient_id} Vital Signs Alert"
                        email_sent = alert_system._send_email(
                            alert_system.default_recipient_email,
                            subject,
                            alert_message
                        )
                        
                        # Also send through regular alert system
                        alert_system_result = alert_system.check_vitals(patient_id, vitals_for_alert)
                        
                        if email_sent or alert_system_result:
                            st.success("✅ Alert successfully sent to healthcare providers!")
                        else:
                            st.error("❌ Failed to send alert. Please check alert system configuration.")
                    except Exception as e:
                        st.error(f"Error sending alert: {str(e)}")
            elif not alert_enabled:
                st.info("ℹ️ Alerts are currently disabled. Enable them in the sidebar to receive notifications.")
        
        # Display timeline charts if there are multiple data points
        if len(df) > 1:
            st.subheader("Vital Signs Timeline")

            # Use selected_vitals if provided, else use all available vitals
            timeline_vitals = selected_vitals if selected_vitals is not None else vital_columns

            plot_vitals_timeline(patient_data, timeline_vitals)
        else:
            st.info("Timeline charts require multiple data points. Add more vital sign measurements to see trends.")
        
        return abnormal_vitals
    except Exception as e:
        st.error(f"Error displaying vitals dashboard: {str(e)}")
        import traceback
        st.text(traceback.format_exc())
        return []



def apply_trend_based_adjustments(data_processor, patient_id, predictions, abnormal_vitals):
    """Apply trend-based adjustments to predictions based on vital signs changes"""
    try:
        # Get prediction history
        if not hasattr(data_processor, 'get_prediction_history'):
            return predictions

        history = data_processor.get_prediction_history(patient_id)
        if not history or len(history) < 2:
            return predictions

        # Get current and previous predictions
        history.sort(key=lambda x: x.get('timestamp', 0))
        current_mortality = predictions['mortality']['ensemble'][0]
        current_los = predictions['los']['ensemble'][0]

        # Calculate trend from last 2 predictions
        prev_mortality = history[-1].get('mortality', current_mortality)
        prev_los = history[-1].get('los', current_los)

        # If vitals are improving (fewer abnormal vitals), adjust predictions downward
        current_abnormal_count = len(abnormal_vitals) if abnormal_vitals else 0

        # Get previous abnormal count from patient data if available
        patient_data = data_processor.process_patient_data(patient_id)
        if patient_data and 'timeseries_df' in patient_data:
            df = patient_data['timeseries_df']
            if len(df) > 1:
                # Compare current vs previous vital signs to detect improvement/deterioration
                improvement_factor = calculate_improvement_factor(df, current_abnormal_count)

                # Apply gradual adjustments based on improvement
                if improvement_factor > 0:  # Improving
                    current_mortality = max(current_mortality * (1 - improvement_factor * 0.1), 0.02)
                    current_los = max(current_los * (1 - improvement_factor * 0.05), 2.0)
                elif improvement_factor < 0:  # Deteriorating
                    current_mortality = min(current_mortality * (1 + abs(improvement_factor) * 0.1), 0.35)
                    current_los = min(current_los * (1 + abs(improvement_factor) * 0.05), 12.0)

        # Update predictions
        predictions['mortality']['ensemble'] = np.array([current_mortality])
        predictions['los']['ensemble'] = np.array([current_los])

        return predictions

    except Exception as e:
        print(f"Error in trend-based adjustments: {e}")
        return predictions

def calculate_improvement_factor(df, current_abnormal_count):
    """Calculate improvement factor based on vital signs trends"""
    try:
        if len(df) < 2:
            return 0

        # Get last two measurements
        latest = df.iloc[-1]
        previous = df.iloc[-2]

        improvement_score = 0
        vital_count = 0

        # Check key vitals for improvement/deterioration
        vital_checks = {
            'Heart Rate': (60, 100),
            'O2 Saturation': (95, 100),
            'Systolic BP': (100, 140),
            'Temperature (C)': (36.5, 37.5),
            'Respiratory Rate': (12, 20)
        }

        for vital, (min_normal, max_normal) in vital_checks.items():
            if vital in df.columns:
                try:
                    current_val = float(latest[vital])
                    prev_val = float(previous[vital])

                    # Calculate how close to normal range
                    current_distance = min(abs(current_val - min_normal), abs(current_val - max_normal))
                    prev_distance = min(abs(prev_val - min_normal), abs(prev_val - max_normal))

                    if current_distance < prev_distance:
                        improvement_score += 1  # Improving
                    elif current_distance > prev_distance:
                        improvement_score -= 1  # Deteriorating

                    vital_count += 1
                except (ValueError, TypeError):
                    continue

        # Return normalized improvement factor (-1 to 1)
        return improvement_score / max(vital_count, 1) if vital_count > 0 else 0

    except Exception as e:
        print(f"Error calculating improvement factor: {e}")
        return 0

def display_patient_predictions(model, data_processor, patient_id, abnormal_vitals=None):
    """Display patient outcome predictions with proper formatting"""
    try:
        # Check if all vitals are normal
        all_vitals_normal = abnormal_vitals is None or len(abnormal_vitals) == 0
        
        # Try to simulate recovery if appropriate
        recovery_simulated = simulate_patient_recovery(data_processor, patient_id, all_vitals_normal)
        
        # Get patient features for prediction
        patient_features = data_processor.get_patient_features(patient_id)
        
        if patient_features is None or patient_features.empty:
            st.warning("⚠️ Unable to get patient features for prediction.")
            # Return default predictions
            return {
                'mortality': {'ensemble': np.array([0.15])},
                'los': {'ensemble': np.array([4.0])}
            }
        
        # Get predictions with fallback for model errors
        try:
            predictions = model.predict(patient_features)
            if predictions is None:
                raise ValueError("Model returned None")

            # Apply trend-based adjustments if we have prediction history
            predictions = apply_trend_based_adjustments(data_processor, patient_id, predictions, abnormal_vitals)

        except Exception as e:
            print(f"Error using model to predict: {e}")
            # Create rule-based predictions based on vital signs status
            abnormal_count = len(abnormal_vitals) if abnormal_vitals else 0

            if all_vitals_normal:
                # For normal vitals: <5% mortality, 2-3 days LOS
                predictions = {
                    'mortality': {'ensemble': np.array([0.035])},  # 3.5% mortality
                    'los': {'ensemble': np.array([2.8])}           # 2.8 days LOS
                }
            elif abnormal_count < 2:
                # For <2 abnormal vitals: <15% mortality, <5 days LOS
                predictions = {
                    'mortality': {'ensemble': np.array([0.10])},   # 10% mortality
                    'los': {'ensemble': np.array([4.2])}           # 4.2 days LOS
                }
            else:
                # For >=2 abnormal vitals: higher but realistic predictions
                predictions = {
                    'mortality': {'ensemble': np.array([0.22])},   # 22% mortality
                    'los': {'ensemble': np.array([7.5])}           # 7.5 days LOS
                }
            st.warning("⚠️ Using rule-based predictions as models are not available.")
        
        # Always store prediction in history
        if hasattr(data_processor, 'store_prediction_history'):
            data_processor.store_prediction_history(patient_id, predictions)
        
        # Display the predictions
        st.subheader("Patient Outcome Predictions")
        
        # Format mortality as percentage
        mortality = predictions['mortality']['ensemble'][0] * 100
        
        # Format length of stay in days
        los = predictions['los']['ensemble'][0]
        
        # Calculate risk factor
        risk_factor = (mortality * 0.7) + (min(los, 14) / 14 * 30)
        
        # Create columns for display
        col1, col2, col3 = st.columns(3)
        
        # Display mortality prediction
        col1.metric(
            "Mortality Risk",
            f"{mortality:.1f}%",
            delta=None
        )
        
        # Display length of stay prediction
        col2.metric(
            "Estimated Length of Stay",
            f"{los:.1f} days",
            delta=None
        )
        
        # Display overall risk factor
        col3.metric(
            "Overall Risk Factor",
            f"{risk_factor:.1f}%",
            delta=None
        )
        
        # Add risk level indicators with more distinct thresholds
        if mortality >= 30.0 or los >= 7.0 or risk_factor >= 35.0:
            st.error("🚨 Patient has high risk factors. Immediate attention required.")
        elif mortality >= 20.0 or los >= 5.0 or risk_factor >= 25.0:
            st.warning("⚠️ Patient has elevated risk factors. Close monitoring recommended.")
        elif mortality < 10.0 and los < 3.0 and risk_factor < 15.0:
            st.info("ℹ️ Patient's condition appears stable with low risk factors.")
        
        # Calculate and display recovery trend
        trend_data = calculate_recovery_trend(data_processor, patient_id)
        if trend_data and trend_data['recovery_status'] != 'Not enough data':
            st.subheader("Recovery Trend Analysis")
            
            # Create columns for trend indicators
            trend_col1, trend_col2, trend_col3 = st.columns(3)
            
            # Display mortality trend
            mortality_trend = trend_data['mortality_trend']
            mortality_icon = "📈" if mortality_trend == "improving" else ("📉" if mortality_trend == "worsening" else "➡️")
            trend_col1.metric(
                "Mortality Risk", 
                f"{mortality_icon} {mortality_trend.title()}", 
                f"{trend_data['mortality_change']:.1f}%"
            )
            
            # Display LOS trend
            los_trend = trend_data['los_trend']
            los_icon = "📈" if los_trend == "improving" else ("📉" if los_trend == "worsening" else "➡️")
            trend_col2.metric(
                "Length of Stay", 
                f"{los_icon} {los_trend.title()}", 
                f"{trend_data['los_change']:.1f} days"
            )
            
            # Display overall recovery status
            status = trend_data['recovery_status']
            status_icon = "✅" if "improving" in status.lower() else ("⚠️" if "attention" in status.lower() or "deteriorating" in status.lower() else "ℹ️")
            trend_col3.metric("Recovery Status", f"{status_icon} {status}")
            
            # Show recovery notification if patient is improving
            if "improving" in status.lower():
                st.success("🎉 Patient is showing signs of recovery! Continue monitoring.")
            elif "deteriorating" in status.lower():
                st.error("⚠️ Patient's condition is deteriorating. Immediate attention recommended.")
        
        return predictions
    except Exception as e:
        st.error(f"Error displaying predictions: {str(e)}")
        import traceback
        st.text(traceback.format_exc())
        return None

def display_patient_demographics(patient_data):
    """Display patient demographics with proper formatting for gender and weight"""
    if 'demographics' in patient_data and patient_data['demographics']:
        st.subheader("Patient Demographics")
        demo = patient_data['demographics']
        
        # Create columns for demographics
        col1, col2, col3 = st.columns(3)
        
        # Display demographics in columns with proper error handling
        # Age
        if 'age' in demo and demo['age'] is not None:
            try:
                age_value = float(demo['age'])
                col1.metric("Age", f"{age_value:.0f} years")
            except (ValueError, TypeError):
                col1.metric("Age", "Unknown")
        else:
            col1.metric("Age", "Unknown")
        
        # Gender - initialize with default value
        gender_text = "Unknown"
        if 'gender' in demo and demo['gender'] is not None:
            gender_value = demo['gender']
            # Convert numeric gender to text with updated mapping
            if isinstance(gender_value, (int, float)) or (isinstance(gender_value, str) and gender_value.isdigit()):
                gender_map = {
                    1: "Female",
                    2: "Male",
                    0: "Unknown",
                    "1": "Female",
                    "2": "Male",
                    "0": "Unknown"
                }
                gender_text = gender_map.get(gender_value, "Unknown")
            else:
                # Handle text gender values
                gender_text = str(gender_value).strip()
                # Capitalize first letter and standardize common values
                if gender_text.lower() in ["m", "male", "man"]:
                    gender_text = "Male"
                elif gender_text.lower() in ["f", "female", "woman"]:
                    gender_text = "Female"
        # If gender not found, check for sex
        elif 'sex' in demo and demo['sex'] is not None:
            sex_value = demo['sex']
            if isinstance(sex_value, str):
                if sex_value.lower() in ["m", "male"]:
                    gender_text = "Male"
                elif sex_value.lower() in ["f", "female"]:
                    gender_text = "Female"
            # Handle numeric sex values
            elif isinstance(sex_value, (int, float)) or (isinstance(sex_value, str) and sex_value.isdigit()):
                sex_map = {
                    1: "Female",
                    2: "Male",
                    0: "Unknown",
                    "1": "Female",
                    "2": "Male",
                    "0": "Unknown"
                }
                gender_text = sex_map.get(sex_value, "Unknown")
        
        col2.metric("Gender", gender_text)
        
        # For weight - check admissionweight column first
        weight_value = None
        # First try admissionweight
        if 'admissionweight' in demo and demo['admissionweight'] is not None:
            try:
                if isinstance(demo['admissionweight'], str):
                    weight_str = ''.join(c for c in demo['admissionweight'] if c.isdigit() or c == '.')
                    weight_value = float(weight_str) if weight_str else None
                else:
                    weight_value = float(demo['admissionweight'])
            except (ValueError, TypeError):
                weight_value = None

        # If admissionweight failed, try regular weight
        if weight_value is None and 'weight' in demo and demo['weight'] is not None:
            try:
                if isinstance(demo['weight'], str):
                    weight_str = ''.join(c for c in demo['weight'] if c.isdigit() or c == '.')
                    weight_value = float(weight_str) if weight_str else None
                else:
                    weight_value = float(demo['weight'])
            except (ValueError, TypeError):
                weight_value = None

        # Display the weight
        if weight_value is not None:
            col3.metric("Weight", f"{weight_value:.1f} kg")
        else:
            col3.metric("Weight", "Unknown")
        
        # Add more demographics in additional rows if available
        col1, col2, col3 = st.columns(3)
        
        # Height
        if 'height' in demo and demo['height'] is not None:
            try:
                height_value = float(demo['height'])
                col1.metric("Height", f"{height_value:.1f} cm")
            except (ValueError, TypeError):
                col1.metric("Height", "Unknown")
        
        # BMI
        if 'bmi' in demo and demo['bmi'] is not None:
            try:
                bmi_value = float(demo['bmi'])
                col2.metric("BMI", f"{bmi_value:.1f}")
            except (ValueError, TypeError):
                # Try to calculate BMI if height and weight are available
                if weight_value is not None and 'height' in demo and demo['height'] is not None:
                    try:
                        height_value = float(demo['height'])
                        if height_value > 0:
                            bmi_value = weight_value / ((height_value/100) ** 2)
                            col2.metric("BMI", f"{bmi_value:.1f}")
                        else:
                            col2.metric("BMI", "Unknown")
                    except (ValueError, TypeError):
                        col2.metric("BMI", "Unknown")
                else:
                    col2.metric("BMI", "Unknown")
        
        # Ethnicity
        if 'ethnicity' in demo and demo['ethnicity'] is not None:
            col3.metric("Ethnicity", str(demo['ethnicity']))
        elif 'race' in demo and demo['race'] is not None:
            col3.metric("Ethnicity", str(demo['race']))
        
        # Display medical history if available
        if 'medical_history' in patient_data and patient_data['medical_history']:
            st.subheader("Medical History")
            
            med_history = patient_data['medical_history']
            history_items = []
            
            for condition, has_condition in med_history.items():
                if has_condition:
                    # Format condition name for display (replace underscores with spaces, capitalize)
                    display_name = condition.replace('_', ' ').title()
                    history_items.append(display_name)
            
            if history_items:
                # Display as pills/tags
                st.write(" • ".join(history_items))
            else:
                st.info("No significant medical history recorded")
    else:
        st.info("No demographic information available for this patient")

def calculate_recovery_trend(data_processor, patient_id):
    """Calculate recovery trend based on prediction history"""
    try:
        history = data_processor.get_prediction_history(patient_id)
        
        if not history or len(history) < 2:
            return {
                'los_trend': 'stable',
                'mortality_trend': 'stable',
                'los_change': 0,
                'mortality_change': 0,
                'recovery_status': 'Not enough data'
            }
        
        # Sort by timestamp to ensure correct order
        history.sort(key=lambda x: x.get('timestamp', 0))
        
        # Get the most recent and previous predictions
        current = history[-1]
        previous = history[-2]
        
        # Calculate changes
        los_change = previous.get('los', 0) - current.get('los', 0)
        mortality_change = previous.get('mortality', 0) - current.get('mortality', 0)
        mortality_change_pct = mortality_change * 100  # Convert to percentage points
        
        # Determine trends
        los_trend = 'improving' if los_change > 0.5 else ('worsening' if los_change < -0.5 else 'stable')
        mortality_trend = 'improving' if mortality_change > 0.02 else ('worsening' if mortality_change < -0.02 else 'stable')
        
        # Determine overall recovery status
        if los_trend == 'improving' and mortality_trend == 'improving':
            recovery_status = 'Rapidly improving'
        elif los_trend == 'improving' or mortality_trend == 'improving':
            recovery_status = 'Improving'
        elif los_trend == 'worsening' and mortality_trend == 'worsening':
            recovery_status = 'Condition deteriorating'
        elif los_trend == 'worsening' or mortality_trend == 'worsening':
            recovery_status = 'Needs attention'
        else:
            recovery_status = 'Stable'
        
        return {
            'los_trend': los_trend,
            'mortality_trend': mortality_trend,
            'los_change': los_change,
            'mortality_change': mortality_change_pct,  # Return as percentage points
            'recovery_status': recovery_status
        }
    except Exception as e:
        print(f"Error calculating recovery trend: {e}")
        import traceback
        traceback.print_exc()
        return {
            'los_trend': 'unknown',
            'mortality_trend': 'unknown',
            'los_change': 0,
            'mortality_change': 0,
            'recovery_status': 'Error calculating trend'
        }

def simulate_patient_recovery(data_processor, patient_id, all_vitals_normal=False):
    """Simulate gradual recovery for patients with normal vitals"""
    try:
        # Get the most recent prediction
        history = data_processor.get_prediction_history(patient_id)
        if not history:
            return False
        
        # Sort by timestamp to ensure correct order
        history.sort(key=lambda x: x.get('timestamp', 0))
        latest_prediction = history[-1]
        
        # Only simulate recovery if it's been at least 30 minutes since last prediction
        import datetime
        now = datetime.datetime.now()
        last_time = latest_prediction.get('timestamp', now - datetime.timedelta(hours=1))
        
        # Convert string timestamp to datetime if needed
        if isinstance(last_time, str):
            try:
                last_time = datetime.datetime.fromisoformat(last_time.replace('Z', '+00:00'))
            except:
                last_time = datetime.datetime.strptime(last_time, '%Y-%m-%d %H:%M:%S')
        
        time_diff = (now - last_time).total_seconds() / 60  # minutes
        
        if time_diff < 30:
            return False  # Not enough time has passed
            
        # Calculate recovery rate based on current values and whether vitals are normal
        mortality = latest_prediction.get('mortality', 0.15)
        los = latest_prediction.get('los', 4.0)
        
        # Faster recovery for normal vitals, slower for abnormal
        mortality_reduction = 0.005 if all_vitals_normal else 0.002  # 0.5% vs 0.2% reduction
        los_reduction = 0.05 if all_vitals_normal else 0.02  # 0.05 vs 0.02 days reduction
        
        # Apply reductions with minimum thresholds
        new_mortality = max(0.01, mortality - mortality_reduction)  # Minimum 1% mortality
        new_los = max(1.0, los - los_reduction)  # Minimum 1 day LOS
        
        # Only update if there's a meaningful change
        if new_mortality < mortality or new_los < los:
            # Create new prediction
            new_prediction = {
                'mortality': {'ensemble': np.array([new_mortality])},
                'los': {'ensemble': np.array([new_los])}
            }
            
            # Store the simulated recovery prediction
            data_processor.store_prediction_history(patient_id, new_prediction)
            return True
            
        return False
    except Exception as e:
        print(f"Error simulating recovery: {e}")
        return False

def add_update_vitals_ui(data_processor, patient_id):
    """Add UI for updating patient vitals"""
    st.subheader("Update Patient Vitals")
    
    # Create columns for better layout
    col1, col2 = st.columns(2)
    
    # Heart rate input
    heart_rate = col1.number_input("Heart Rate (bpm)", min_value=0, max_value=300, value=75, step=1)
    
    # O2 saturation input
    o2_sat = col1.number_input("O2 Saturation (%)", min_value=0, max_value=100, value=98, step=1)
    
    # Blood pressure inputs
    systolic = col2.number_input("Systolic BP (mmHg)", min_value=0, max_value=1000, value=120, step=1)
    diastolic = col2.number_input("Diastolic BP (mmHg)", min_value=0, max_value=500, value=80, step=1)
    
    # Temperature input
    temp = col2.number_input("Temperature (C)", min_value=30.0, max_value=45.0, value=37.0, step=0.1)
    
    # Respiratory rate input
    resp_rate = col1.number_input("Respiratory Rate (breaths/min)", min_value=3, max_value=40, value=16, step=1)

    # Glucose input
    glucose = col2.number_input("Glucose (mg/dL)", min_value=0, max_value=1000, value=100, step=1)
    
    # Submit button
    if st.button("Submit New Vitals"):
        # Calculate MAP
        map_value = (systolic + 2 * diastolic) / 3
        
        # Create vitals dictionary
        new_vitals = {
            'Heart Rate': heart_rate,
            'O2 Saturation': o2_sat,
            'Systolic BP': systolic,
            'Diastolic BP': diastolic,
            'MAP (mmHg)': map_value,
            'Temperature (C)': temp,
            'Respiratory Rate': resp_rate,
            'Glucose': glucose
        }
        
        # Update patient data
        success = data_processor.update_patient_vitals(patient_id, new_vitals)
        
        if success:
            st.success("✅ Patient vitals updated successfully!")
            # Add a button to refresh the page
            st.button("Refresh Display")
        else:
            st.error("❌ Failed to update patient vitals.")

def display_prediction_history(data_processor, patient_id):
    """Display prediction history for a patient"""
    try:
        if not hasattr(data_processor, 'get_prediction_history'):
            st.info("Prediction history tracking is not available.")
            return
            
        history = data_processor.get_prediction_history(patient_id)
        
        if not history or len(history) < 2:
            st.info("Not enough prediction history available for trend analysis.")
            return
        
        # Display prediction history chart
        st.subheader("Prediction History")
        
        # Convert history to DataFrame for easier plotting
        import pandas as pd
        history_df = pd.DataFrame(history)
        
        # Convert mortality to percentage (multiply by 100)
        if 'mortality' in history_df.columns and not history_df['mortality'].isna().all():
            history_df['mortality'] = history_df['mortality'] * 100
        
        # Create two columns for the charts
        col1, col2 = st.columns(2)
        
        # Plot mortality trend
        if 'mortality' in history_df.columns and not history_df['mortality'].isna().all():
            import plotly.express as px
            fig1 = px.line(
                history_df, 
                x='timestamp', 
                y='mortality',
                title='Mortality Risk Trend (%)',
                markers=True
            )
            fig1.update_layout(height=300)
            col1.plotly_chart(fig1, use_container_width=True)
        
        # Plot length of stay trend
        if 'los' in history_df.columns and not history_df['los'].isna().all():
            fig2 = px.line(
                history_df, 
                x='timestamp', 
                y='los',
                title='Length of Stay Trend (days)',
                markers=True
            )
            fig2.update_layout(height=300)
            col2.plotly_chart(fig2, use_container_width=True)
        
        # Add trend analysis
        trend_data = calculate_recovery_trend(data_processor, patient_id)
        if trend_data:
            st.subheader("Recovery Trend Analysis")
            
            # Create columns for trend indicators
            trend_col1, trend_col2, trend_col3 = st.columns(3)
            
            # Display mortality trend
            mortality_trend = trend_data['mortality_trend']
            mortality_icon = "📈" if mortality_trend == "improving" else ("📉" if mortality_trend == "worsening" else "➡️")
            trend_col1.metric(
                "Mortality Risk", 
                f"{mortality_icon} {mortality_trend.title()}", 
                f"{trend_data['mortality_change']:.1f}%"
            )
            
            # Display LOS trend
            los_trend = trend_data['los_trend']
            los_icon = "📈" if los_trend == "improving" else ("📉" if los_trend == "worsening" else "➡️")
            trend_col2.metric(
                "Length of Stay", 
                f"{los_icon} {los_trend.title()}", 
                f"{trend_data['los_change']:.1f} days"
            )
            
            # Display overall recovery status
            status = trend_data['recovery_status']
            status_icon = "✅" if "improving" in status.lower() else ("⚠️" if "attention" in status.lower() else "ℹ️")
            trend_col3.metric("Recovery Status", f"{status_icon} {status}")
        
    except Exception as e:
        st.warning(f"Could not display prediction history: {str(e)}")
        import traceback
        print(traceback.format_exc())

def train_model_from_data(model, features_df):
    """Train the model with the provided data"""
    try:
        print(f"Training model with {len(features_df)} samples")
        
        # Check if we have the required outcome columns
        has_mortality = 'mortality' in features_df.columns
        has_los = 'los' in features_df.columns or 'lengthofstay' in features_df.columns
        
        if not (has_mortality or has_los):
            print("Adding default outcome columns")
            features_df['mortality'] = 0  # Default: survived
            features_df['los'] = 5.0      # Default: 5 days
            has_mortality = True
            has_los = True
        
        # Ensure all columns are numeric
        for col in features_df.columns:
            if col not in ['mortality', 'los', 'lengthofstay'] and features_df[col].dtype == 'object':
                try:
                    features_df[col] = pd.to_numeric(features_df[col], errors='coerce')
                except:
                    print(f"Could not convert column {col} to numeric")
        
        # Fill missing values
        features_df = features_df.fillna(0)
        
        # Print summary of data
        print(f"Features shape: {features_df.shape}")
        print(f"Columns: {features_df.columns.tolist()}")
        print(f"Has mortality: {has_mortality}, Has LOS: {has_los}")
        
        # Train the model
        training_success = model.train(features_df)
        
        if training_success:
            print("Model trained successfully!")
            return True
        else:
            print("Failed to train model")
            # Try to create simple models as fallback
            print("Creating simple models as fallback")
            return create_simple_models(model)
    except Exception as e:
        print(f"Error training model: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_models(model):
    """
    Create simple fallback models for mortality and length of stay.
    """
    try:
        # Simple dummy model: always predicts 0.15 mortality and 4.0 days LOS
        class DummyModel:
            def predict(self, X):
                return {
                    'mortality': {'ensemble': np.array([0.15] * len(X))},
                    'los': {'ensemble': np.array([4.0] * len(X))}
                }
            def train(self, X):
                return True

        model.mortality_model = DummyModel()
        model.los_model = DummyModel()
        return True
    except Exception as e:
        print(f"Error creating simple models: {e}")
        return False

def plot_vitals_timeline(patient_data, selected_vitals):
    """Plot vital signs with real-time timestamps"""
    import matplotlib.dates as mdates
    if not patient_data or 'timeseries_df' not in patient_data or patient_data['timeseries_df'] is None:
        st.info("No vital signs data available for this patient.")
        return

    df = patient_data['timeseries_df'].copy()

    # Find timestamp column
    timestamp_cols = ['timestamp', 'datetime', 'time', 'observationtime']
    timestamp_col = None
    for col in timestamp_cols:
        if col in df.columns:
            timestamp_col = col
            break

    # If no timestamp column found, create real-time timestamps
    if timestamp_col is None:
        from datetime import datetime, timedelta
        now = datetime.now()

        # Create timestamps based on when data was created/updated
        if 'time_index' in df.columns:
            # Use time_index to create realistic timestamps
            df['timestamp'] = [now - timedelta(minutes=(len(df)-i-1)*30) for i in range(len(df))]
        else:
            # Create time_index and timestamps
            df['time_index'] = range(len(df))
            df['timestamp'] = [now - timedelta(minutes=(len(df)-i-1)*30) for i in range(len(df))]

        timestamp_col = 'timestamp'
    else:
        # Ensure timestamp column is datetime
        try:
            df[timestamp_col] = pd.to_datetime(df[timestamp_col])
        except:
            # If conversion fails, create new timestamps
            from datetime import datetime, timedelta
            now = datetime.now()
            df[timestamp_col] = [now - timedelta(minutes=(len(df)-i-1)*30) for i in range(len(df))]
    
    # Filter to only include selected vitals
    vital_columns = [v for v in selected_vitals if v in df.columns]
    
    if not vital_columns:
        st.info("No selected vital signs found in patient data.")
        return
    
    # Create a figure for each vital sign
    for vital in vital_columns:
        try:
            # Create a line chart
            fig, ax = plt.subplots(figsize=(10, 4))
            ax.plot(df[timestamp_col], df[vital], marker='o')
            ax.set_title(f"{vital} Over Time")
            ax.set_ylabel(vital)
            ax.set_xlabel("Time")
            ax.grid(True)
            
            # Format x-axis to show time only (HH:MM)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            fig.autofmt_xdate()
            
            # Add threshold lines if applicable
            alert_thresholds = {
                'Heart Rate': [47, 150],
                'HR': [47, 150],
                'Pulse': [47, 150],
                'O2 Saturation': [90, 100],
                'SPO2': [90, 100],
                'Oxygen': [90, 100],
                'MAP (mmHg)': [50, 180],
                'MAP': [50, 180],
                'Temperature (C)': [36.0, 39.5],
                'Temperature': [36.0, 39.5],
                'Temp': [36.0, 39.5],
                'Glucose': [70, 180],
                'GCS': [3, 15],
                'Respiratory Rate': [8, 30],
                'RR': [8, 30],
                'Resp': [8, 30],
                'Systolic BP': [90, 160],
                'SBP': [90, 160],
                'Diastolic BP': [60, 90],
                'DBP': [60, 90],
                'Blood Pressure': [90, 160]
            }
            
            # Find matching threshold (case insensitive)
            threshold = None
            for threshold_name, threshold_values in alert_thresholds.items():
                if threshold_name.lower() in vital.lower() or vital.lower() in threshold_name.lower():
                    threshold = threshold_values
                    break
            
            if threshold:
                min_val, max_val = threshold
                ax.axhline(y=min_val, color='r', linestyle='--', label=f'Min: {min_val}')
                ax.axhline(y=max_val, color='r', linestyle='--', label=f'Max: {max_val}')
                ax.legend()
            
            # Display the chart
            st.pyplot(fig)
            plt.close(fig)
        except Exception as e:
            st.error(f"Error plotting {vital}: {str(e)}")
    
    return

def display_quick_vitals_update(data_processor, patient_id):
    """Display a quick update panel for vital signs"""
    st.subheader("Quick Vitals Update")
    
    with st.form("quick_vitals_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=0, step=1)
            o2_sat = st.number_input("O2 Saturation (%)", min_value=0, max_value=100, step=1)
            temp = st.number_input("Temperature (C)", min_value=36.0, step=0.1, format="%.1f")
            glucose = st.number_input("Glucose (mg/dL)", min_value=0, step=1)
            
        with col2:
            systolic = st.number_input("Systolic BP (mmHg)", min_value=0, step=1)
            diastolic = st.number_input("Diastolic BP (mmHg)", min_value=0, step=1)
            resp_rate = st.number_input("Respiratory Rate", min_value=0, step=1)
        
        submit = st.form_submit_button("Update Vitals")
        
        if submit:
            # Calculate MAP
            map_value = (systolic + 2 * diastolic) / 3
            
            # Create vitals dictionary
            new_vitals = {
                'Heart Rate': heart_rate,
                'O2 Saturation': o2_sat,
                'Systolic BP': systolic,
                'Diastolic BP': diastolic,
                'MAP (mmHg)': map_value,
                'Temperature (C)': temp,
                'Respiratory Rate': resp_rate,
                'Glucose': glucose
            }
            
            # Update patient data
            success = data_processor.update_patient_vitals(patient_id, new_vitals)
            
            if success:
                st.success("✅ Patient vitals updated successfully!")
                # Set a flag in session state to indicate vitals were updated
                st.session_state['vitals_updated'] = True
                st.session_state['last_update_time'] = pd.Timestamp.now()

                # Try different rerun methods based on Streamlit version
                try:
                    # Check if experimental_rerun exists (older versions)
                    if hasattr(st, 'experimental_rerun'):
                        st.experimental_rerun()
                        return True
                    else:
                        # Fallback: show manual refresh instruction
                        st.info("Please refresh the page manually (F5 or Ctrl+R) to see updated vitals and timeline.")
                        return True
                except Exception as e:
                    st.info(f"Please refresh the page manually to see updates. Error: {str(e)}")
                    return True
            else:
                st.error("❌ Failed to update patient vitals.")
                return False
    
    return False

def add_manual_patient_data():
    """Add a new patient with manual data entry"""
    st.subheader("Add New Patient")
    
    with st.form("manual_patient_form"):
        # Patient ID
        import time
        patient_id = st.text_input("Patient ID", value=f"MANUAL_{int(time.time())}")
        
        # Demographics
        st.subheader("Demographics")
        col1, col2 = st.columns(2)
        with col1:
            age = st.number_input("Age", min_value=0, step=1)
            gender = st.selectbox("Gender", ["Male", "Female", "Other"])
        with col2:
            ethnicity = st.selectbox("Ethnicity", ["Caucasian", "African American", "Hispanic", "Asian", "Other"])
            admission_type = st.selectbox("Admission Type", ["Emergency", "Elective", "Urgent"])
        
        # Medical History
        st.subheader("Medical History")
        col1, col2 = st.columns(2)
        with col1:
            heart_disease = st.checkbox("Heart Disease")
            diabetes = st.checkbox("Diabetes")
            hypertension = st.checkbox("Hypertension")
        with col2:
            respiratory_disease = st.checkbox("Respiratory Disease")
            kidney_disease = st.checkbox("Kidney Disease")
            liver_disease = st.checkbox("Liver Disease")
        
        # Vital Signs
        st.subheader("Initial Vital Signs")
        col1, col2, col3 = st.columns(3)
        with col1:
            heart_rate = st.number_input("Heart Rate (bpm)", min_value=0, step=1)
            systolic_bp = st.number_input("Systolic BP (mmHg)", min_value=0, step=1)
            diastolic_bp = st.number_input("Diastolic BP (mmHg)", min_value=0, step=1)
        with col2:
            o2_sat = st.number_input("O2 Saturation (%)", min_value=0, step=1)
            temp = st.number_input("Temperature (C)", min_value=0.0, step=0.1, format="%.1f")
            glucose = st.number_input("Glucose (mg/dL)", min_value=0, step=1)
        with col3:
            resp_rate = st.number_input("Respiratory Rate", min_value=0, step=1)
            gcs = st.number_input("GCS", min_value=3, max_value=15, step=1)
            fio2 = st.number_input("FiO2 (%)", min_value=21, max_value=100, step=1)
        
        # Submit button
        submitted = st.form_submit_button("Add Patient")

def main():
    st.title("ICU Patient Data Visualization")
    
    # Initialize session state for tab and data source selection
    if 'tab' not in st.session_state:
        st.session_state['tab'] = "Data Loading"
    
    if 'data_source' not in st.session_state:
        st.session_state['data_source'] = "Upload data file"
    
    # Initialize data processor and alert system
    try:
        data_processor = load_data_processor()
        if data_processor is None:
            st.error("Failed to initialize data processor")
            # Create a minimal data processor that won't cause errors
            data_processor = type('DummyDataProcessor', (), {
                'process_patient_data': lambda *args, **kwargs: None,
                'get_patient_features': lambda *args, **kwargs: None,
                'patient_dirs': [],
                'patients_data': {}  # Add this to ensure it's never None
            })
        
        # Always store data_processor in session state
        st.session_state['data_processor'] = data_processor
        
    except Exception as e:
        st.error(f"Error initializing data processor: {str(e)}")
        # Create a minimal data processor that won't cause errors
        data_processor = type('DummyDataProcessor', (), {
            'process_patient_data': lambda *args, **kwargs: None,
            'get_patient_features': lambda *args, **kwargs: None,
            'patient_dirs': [],
            'patients_data': {}  # Add this to ensure it's never None
        })
        # Store even the dummy processor in session state
        st.session_state['data_processor'] = data_processor
    
    try:
        alert_system = AlertSystem()
        
        # Verify patient_configs is initialized
        if not hasattr(alert_system, 'patient_configs'):
            alert_system.patient_configs = {}
        
        # Set default recipient email if not already set
        if not hasattr(alert_system, 'default_recipient_email'):
            alert_system.default_recipient_email = "<EMAIL>"
        
        # Ensure email is enabled
        if 'email' in alert_system.config:
            alert_system.config['email']['enabled'] = True
        
        # Print email configuration for debugging
        print(f"Email configuration: {alert_system.config['email']}")
        print(f"Default recipient email: {alert_system.default_recipient_email}")
    except Exception as e:
        st.error(f"Error initializing alert system: {str(e)}")
        # Create a minimal alert system that won't cause errors
        alert_system = type('DummyAlertSystem', (), {
            'patient_configs': {},
            'check_vitals': lambda *args, **kwargs: False
        })
    
    # Import or define load_ensemble_model before use
    from src.models.ensemble_model import EnsembleICUModel
    def load_ensemble_model():
        return EnsembleICUModel()
    model = load_ensemble_model()
    
    # Initialize alert_enabled variable
    alert_enabled = True
    
    # Create sidebar
    st.sidebar.title("ICU Monitoring System")
    
    # Create sidebar tabs
    tab = st.sidebar.selectbox(
        "Select Option",
        ["Data Loading", "Alert Settings", "Test Alerts", "Model Training"],
        index=["Data Loading", "Alert Settings", "Test Alerts", "Model Training"].index(st.session_state['tab'])
    )
    
    # Update session state
    st.session_state['tab'] = tab
    
    # Data Loading Tab
    if tab == "Data Loading":
        st.sidebar.header("Data Loading")
        
        data_source = st.sidebar.radio(
            "Choose data source",
            ["Upload data file", "Enter patient data manually"],
            index=["Upload data file", "Enter patient data manually"].index(st.session_state['data_source'])
        )
        
        # Update session state
        st.session_state['data_source'] = data_source
        
        if data_source == "Upload data file":
            uploaded_file = st.sidebar.file_uploader("Upload ICU patient data", type=["csv", "zip"])
            
            if uploaded_file is not None:
                # Use a sidebar status message instead of spinner
                status_message = st.sidebar.empty()
                status_message.info("Loading data...")
                
                # Save to temp file
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(uploaded_file.name)[1]) as tmp_file:
                    tmp_file.write(uploaded_file.getvalue())
                    tmp_file_path = tmp_file.name
                
                # Process data
                if uploaded_file.name.endswith('.zip'):
                    success = data_processor.load_from_zip(tmp_file_path)
                else:
                    success = data_processor.load_from_csv(tmp_file_path)
                
                # Clean up
                os.unlink(tmp_file_path)
                
                # Update status message
                if success:
                    status_message.success(f"Data loaded successfully! Found {len(data_processor.patient_dirs)} patients.")
                else:
                    status_message.error("Failed to load data. Please check the file format.")
        
        elif data_source == "Enter patient data manually":
            st.sidebar.subheader("Manual Patient Data Entry")
            
            with st.sidebar.form("manual_data_form"):
                st.write("Patient Demographics")
                patient_id = st.text_input("Patient ID", value="MANUAL_001")
                age = st.number_input("Age", min_value=0, max_value=120, value=65)
                gender = st.selectbox("Gender", ["Male", "Female", "Other"])
                weight = st.number_input("Weight (kg)", min_value=0.0, max_value=300.0, value=70.0, step=0.1)
                height = st.number_input("Height (cm)", min_value=0.0, max_value=250.0, value=170.0, step=0.1)
                
                # Calculate BMI automatically
                bmi = weight / ((height/100) ** 2) if height > 0 else 0
                st.write(f"BMI: {bmi:.1f}")
                
                st.write("Medical History (will be displayed in patient view)")
                has_diabetes = st.checkbox("Diabetes")
                has_hypertension = st.checkbox("Hypertension")
                has_heart_disease = st.checkbox("Heart Disease")
                has_respiratory_disease = st.checkbox("Respiratory Disease")
                has_kidney_disease = st.checkbox("Kidney Disease")
                
                st.write("Vital Signs")
                heart_rate = st.number_input("Heart Rate (bpm)", min_value=0, max_value=300, value=75)
                o2_saturation = st.number_input("O2 Saturation (%)", min_value=0, max_value=100, value=98)
                systolic_bp = st.number_input("Systolic BP (mmHg)", min_value=0, max_value=1000, value=120)
                diastolic_bp = st.number_input("Diastolic BP (mmHg)", min_value=0, max_value=500, value=80)
                respiratory_rate = st.number_input("Respiratory Rate (breaths/min)", min_value=0, max_value=100, value=16)
                temperature = st.number_input("Temperature (°C)", min_value=30.0, max_value=45.0, value=37.0, format="%.1f")
                
                st.write("Lab Values")
                glucose = st.number_input("Glucose (mg/dL)", min_value=0, max_value=1000, value=100)
                wbc = st.number_input("WBC Count (×10^9/L)", min_value=0.0, max_value=80.0, value=7.5, step=0.1)
                creatinine = st.number_input("Creatinine (mg/dL)", min_value=0.0, max_value=20.0, value=1.0, step=0.1)
                
                # Submit button
                submit_button = st.form_submit_button("Create Patient Record")
            
            if submit_button:
                # Create manual patient data
                status_message = st.sidebar.empty()
                status_message.info("Creating patient record...")
                
                try:
                    # Create demographics
                    demographics = {
                        'patientunitstayid': patient_id,
                        'age': age,
                        'gender': gender,
                        'weight': weight,
                        'height': height,
                        'bmi': bmi
                    }
                    
                    # Create medical history
                    medical_history = {
                        'diabetes': has_diabetes,
                        'hypertension': has_hypertension,
                        'heart_disease': has_heart_disease,
                        'respiratory_disease': has_respiratory_disease,
                        'kidney_disease': has_kidney_disease
                    }
                    
                    # Create timeseries with 3 timepoints
                    times = list(range(3))
                    
                    # Use exact entered vital signs repeated for 3 timepoints without variation
                    heart_rates = [heart_rate for _ in range(3)]
                    o2_saturations = [o2_saturation for _ in range(3)]
                    systolic_bps = [systolic_bp for _ in range(3)]
                    diastolic_bps = [diastolic_bp for _ in range(3)]
                    respiratory_rates = [respiratory_rate for _ in range(3)]
                    temperatures = [temperature for _ in range(3)]
                    
                    # Calculate MAP exactly for each timepoint
                    maps = [round((systolic_bp + 2 * diastolic_bp) / 3) for _ in range(3)]
                    
                    # Use exact lab values repeated for 3 timepoints without variation
                    glucoses = [glucose for _ in range(3)]
                    wbcs = [wbc for _ in range(3)]
                    creatinines = [creatinine for _ in range(3)]
                    
                    # Create dataframe
                    timeseries_df = pd.DataFrame({
                        'time': times,
                        'Heart Rate': heart_rates,
                        'O2 Saturation': o2_saturations,
                        'Systolic BP': systolic_bps,
                        'Diastolic BP': diastolic_bps,
                        'MAP (mmHg)': maps,
                        'Respiratory Rate': respiratory_rates,
                        'Temperature (C)': temperatures,
                        'Glucose': glucoses,
                        'WBC Count': wbcs,
                        'Creatinine': creatinines
                    })
                    
                    # Create patient data
                    manual_patient_data = {
                        'demographics': demographics,
                        'medical_history': medical_history,
                        'timeseries_df': timeseries_df,
                        'feature_names': timeseries_df.columns.tolist(),
                        'processed': True
                    }
                    
                    # Check if data_processor is None
                    if data_processor is None:
                        st.sidebar.error("Data processor is not initialized. Please refresh the page and try again.")
                        return
                    
                    # Add to data processor
                    if not hasattr(data_processor, 'patients_data'):
                        data_processor.patients_data = {}
                    
                    data_processor.patients_data[patient_id] = manual_patient_data
                    
                    # Add to patient directories
                    if not hasattr(data_processor, 'patient_dirs'):
                        data_processor.patient_dirs = []
                    
                    if patient_id not in data_processor.patient_dirs:
                        data_processor.patient_dirs.append(patient_id)
                    
                    status_message.success(f"Patient {patient_id} created successfully!")
                    
                    # Debug info
                    st.sidebar.write(f"Created patient with ID: {patient_id}")
                    st.sidebar.write(f"Number of patients: {len(data_processor.patient_dirs)}")
                    
                except Exception as e:
                    status_message.error(f"Error creating patient record: {str(e)}")
                    st.sidebar.error(f"Error details: {type(e).__name__}")
                    import traceback
                    st.sidebar.text(traceback.format_exc())
    
    # Alert Settings Tab
    elif tab == "Alert Settings":
        st.sidebar.header("Alert Settings")
        
        # Update the already initialized alert_enabled variable
        alert_enabled = st.sidebar.checkbox("Enable Alerts", value=True)
        
        if alert_enabled:
            st.sidebar.subheader("Alert Thresholds")
            heart_rate_min = st.sidebar.slider("Heart Rate Min (bpm)", 30, 60, 50)
            o2_sat_min = st.sidebar.slider("O2 Saturation Min (%)", 80, 95, 90)
            
            st.sidebar.subheader("Register Your Device")
            notification_type = st.sidebar.selectbox(
                "Notification Method", 
                ["Email", "Push Notification"]
            )
            
            if notification_type == "Email":
                email = st.sidebar.text_input("Email Address")
                
                if st.sidebar.button("Register Email"):
                    if email and '@' in email:
                        # Register email for all patients
                        for patient_id in data_processor.patient_dirs:
                            contact_info = {'email': email}
                            alert_system.set_patient_alert_config(patient_id, contact_info)
                        st.sidebar.success("Email registered successfully!")
                    else:
                        st.sidebar.error("Please enter a valid email address")
            
            elif notification_type == "Push Notification":
                device_token = st.sidebar.text_input("Device Token")
                
                if st.sidebar.button("Register Device"):
                    if device_token:
                        # Register device for all patients
                        for patient_id in data_processor.patient_dirs:
                            contact_info = {'device_token': device_token}
                            alert_system.set_patient_alert_config(patient_id, contact_info)
                        st.sidebar.success("Device registered successfully!")
                    else:
                        st.sidebar.error("Please enter a device token")
    
    # Test Alerts Tab
    elif tab == "Test Alerts":
        st.sidebar.header("Test Alerts")
        
        # Add a button to test the entire alert system
        if st.sidebar.button("Test Alert System"):
            status_message = st.sidebar.empty()
            status_message.info("Testing alert system...")
            
            try:
                # Test the alert system
                test_results = alert_system.test_alert_system()
                
                if test_results["overall"]:
                    status_message.success("✅ Alert system is working!")
                    
                    # Show detailed results
                    st.sidebar.write("### Test Results")
                    if test_results["email"]:
                        st.sidebar.success("✅ Email alerts: Working")
                    else:
                        st.sidebar.error("❌ Email alerts: Not working")
                    
                    if test_results["push"]:
                        st.sidebar.success("✅ Push notifications: Working")
                    else:
                        st.sidebar.error("❌ Push notifications: Not working")
                else:
                    status_message.error("❌ Alert system is not working!")
                    st.sidebar.error("Please check the configuration and try again.")
            except Exception as e:
                status_message.error(f"Error testing alert system: {str(e)}")
                import traceback
                st.sidebar.text(traceback.format_exc())
    
    # Model Training Tab
    elif tab == "Model Training":
        st.sidebar.header("Model Training")
        
        # Add explanation about required data
        st.sidebar.markdown("""
        ### Required Data Format
        To train the models, your data should include:
        - **Patient ID**: patientunitstayid or patient_id
        - **Vital signs**: Heart Rate, O2 Saturation, Respiratory Rate, BP, Temperature
        - **Demographics**: Age, Gender (if available)
        - **Outcomes**: 
            - mortality (0/1)
            - lengthofstay (in hours) or los (in days)
        """)
        
        # Add a section to manually add outcome variables if missing
        st.sidebar.markdown("### Add Missing Outcome Variables")
        add_outcomes = st.sidebar.checkbox("Manually add outcome variables")
        
        if add_outcomes:
            default_mortality = 0.0
            default_los = 5.0
            
            st.sidebar.markdown("Set default values for all patients:")
            default_mortality = st.sidebar.number_input("Default Mortality (0 or 1)", 
                                                       min_value=0.0, max_value=1.0, value=default_mortality, step=1.0)
            default_los = st.sidebar.number_input("Default Length of Stay (days)", 
                                                 min_value=0.1, max_value=100.0, value=default_los, step=0.5)
            
            if st.sidebar.button("Apply Default Outcomes"):
                # Add default outcome variables to all patients
                for patient_id in data_processor.patient_dirs:
                    if patient_id in data_processor.patients_data:
                        # Add mortality and LOS to demographics
                        if 'demographics' in data_processor.patients_data[patient_id]:
                            data_processor.patients_data[patient_id]['demographics']['mortality'] = default_mortality
                            data_processor.patients_data[patient_id]['demographics']['los'] = default_los
                        else:
                            data_processor.patients_data[patient_id]['demographics'] = {
                                'patientunitstayid': patient_id,
                                'mortality': default_mortality,
                                'los': default_los
                            }
                
                st.sidebar.success(f"Added default outcomes to all patients: Mortality={default_mortality}, LOS={default_los} days")
        
        # Add train button
        if st.sidebar.button("Train Model"):
            status_message = st.sidebar.empty()
            status_message.info("Training models...")
            
            try:
                # Get all patient features
                all_features = []
                
                # Debug: Show raw data for first patient
                if data_processor.patient_dirs:
                    first_patient = list(data_processor.patient_dirs)[0]
                    st.write(f"### Raw Data for Patient {first_patient}")
                    
                    # Show raw patient data
                    raw_data = data_processor.process_patient_data(first_patient)
                    if raw_data:
                        if 'demographics_df' in raw_data and raw_data['demographics_df'] is not None:
                            st.write("#### Demographics DataFrame Columns")
                            st.write(raw_data['demographics_df'].columns.tolist())
                            
                            # Check for outcome columns
                            outcome_cols = [col for col in raw_data['demographics_df'].columns 
                                           if col.lower() in ['mortality', 'death', 'died', 'deceased', 
                                                             'lengthofstay', 'lengthof stay', 'los', 'length_of_stay']]
                            if outcome_cols:
                                st.write("#### Found Outcome Columns")
                                st.write(outcome_cols)
                                
                                # Show sample values
                                st.write("#### Sample Values")
                                st.write(raw_data['demographics_df'][outcome_cols].head())
                        
                        if 'timeseries_df' in raw_data and raw_data['timeseries_df'] is not None:
                            st.write("#### Timeseries DataFrame Columns")
                            st.write(raw_data['timeseries_df'].columns.tolist())
                            
                            # Check for outcome columns
                            outcome_cols = [col for col in raw_data['timeseries_df'].columns 
                                           if col.lower() in ['mortality', 'death', 'died', 'deceased', 
                                                             'lengthofstay', 'lengthof stay', 'los', 'length_of_stay']]
                            if outcome_cols:
                                st.write("#### Found Outcome Columns")
                                st.write(outcome_cols)
                                
                                # Show sample values
                                st.write("#### Sample Values")
                                st.write(raw_data['timeseries_df'][outcome_cols].head())
                
                # Extract features for all patients
                for patient_id in data_processor.patient_dirs:
                    features = data_processor.get_patient_features(patient_id)
                    if features is not None:
                        # Debug: Check if outcome variables exist in the features
                        has_mortality = 'mortality' in features.columns
                        has_los = 'los' in features.columns
                        has_lengthofstay = 'lengthofstay' in features.columns
                        
                        # If no outcome variables, try to add them directly
                        if not (has_mortality or has_los or has_lengthofstay):
                            # Get raw patient data
                            raw_data = data_processor.process_patient_data(patient_id)
                            
                            if raw_data:
                                # Try to find outcome variables in demographics_df
                                if 'demographics_df' in raw_data and raw_data['demographics_df'] is not None:
                                    df = raw_data['demographics_df']
                                    
                                    # Check for mortality columns
                                    for col in df.columns:
                                        if col.lower() in ['mortality', 'death', 'died', 'deceased']:
                                            try:
                                                value = df[col].dropna().iloc[0] if not df[col].dropna().empty else 0
                                                features['mortality'] = 1 if (isinstance(value, str) and value.lower() in ['yes', 'true', '1', 'y', 't']) else (1 if float(value) > 0 else 0)
                                                print(f"Added mortality={features['mortality']} from column {col}")
                                                break
                                            except (ValueError, IndexError, TypeError):
                                                continue
                                    
                                    # Check for LOS columns
                                    for col in df.columns:
                                        if col.lower() in ['lengthofstay', 'lengthof stay', 'los', 'length_of_stay']:
                                            try:
                                                value = df[col].dropna().iloc[0] if not df[col].dropna().empty else 5.0
                                                if col.lower() in ['lengthofstay', 'lengthof stay']:
                                                    features['lengthofstay'] = float(value)
                                                    features['los'] = float(value) / 24.0
                                                    print(f"Added lengthofstay={features['lengthofstay']} and los={features['los']} from column {col}")
                                                else:
                                                    features['los'] = float(value)
                                                    print(f"Added los={features['los']} from column {col}")
                                                break
                                            except (ValueError, IndexError, TypeError):
                                                continue
                                
                                # If still no outcome variables, add default values
                                if not ('mortality' in features.columns or 'los' in features.columns or 'lengthofstay' in features.columns):
                                    features['mortality'] = 0  # Default: survived
                                    features['los'] = 5.0      # Default: 5 days
                                    print(f"Added default outcome values: mortality=0, los=5.0")
                    
                    all_features.append(features)
            
                if all_features:
                    # Combine all features
                    features_df = pd.concat(all_features, ignore_index=True)
                    
                    # Display data summary
                    st.write("### Training Data Summary")
                    st.write(f"Number of patients: {len(features_df)}")
                    
                    # Debug: Show all column names
                    st.write("### Available Columns")
                    st.write(features_df.columns.tolist())
                    
                    # Check for outcome variables
                    has_mortality = 'mortality' in features_df.columns
                    has_los = 'los' in features_df.columns
                    has_lengthofstay = 'lengthofstay' in features_df.columns
                    
                    # Debug: Show outcome variable status
                    st.write("### Outcome Variables Status")
                    st.write(f"Has mortality column: {has_mortality}")
                    st.write(f"Has los column: {has_los}")
                    st.write(f"Has lengthofstay column: {has_lengthofstay}")
                    
                    if has_mortality:
                        mortality_count = features_df['mortality'].sum()
                        mortality_rate = mortality_count / len(features_df) * 100
                        st.write(f"Mortality rate: {mortality_rate:.1f}% ({mortality_count} patients)")
                    
                    if has_los:
                        avg_los = features_df['los'].mean()
                        st.write(f"Average length of stay: {avg_los:.1f} days")
                    elif has_lengthofstay:
                        avg_los_hours = features_df['lengthofstay'].mean()
                        avg_los_days = avg_los_hours / 24
                        st.write(f"Average length of stay: {avg_los_hours:.1f} hours ({avg_los_days:.1f} days)")
                    
                    # If still no outcome variables, add them directly to the dataframe
                    if not (has_mortality or has_los or has_lengthofstay):
                        st.warning("No outcome variables found. Adding default values for training.")
                        features_df['mortality'] = 0  # Default: survived
                        features_df['los'] = 5.0      # Default: 5 days
                        has_mortality = True
                        has_los = True
                    
                    # Debug: Check for minimum data requirements
                    if len(features_df) < 10:
                        st.error(f"Not enough data for training. Need at least 10 patients, but only have {len(features_df)}.")
                    elif not (has_mortality or has_los or has_lengthofstay):
                        st.error("Missing required outcome variables. Need either 'mortality', 'los', or 'lengthofstay' column.")
                    
                    # Train the model
                    training_success = model.train(features_df)
                    
                    if training_success:
                        status_message.success("Model trained successfully!")
                    else:
                        status_message.error("Failed to train model. Not enough data or missing outcome variables.")
                else:
                    status_message.error("No patient data available for training.")
            except Exception as e:
                status_message.error(f"Error training model: {str(e)}")
                import traceback
                st.sidebar.text(traceback.format_exc())
        
        # Add a button to create simple models
        if st.sidebar.button("Create Simple Models"):
            with st.sidebar:
                status_message = st.empty()
                status_message.info("Creating simple models...")
                
                success = create_simple_models(model)
                
                if success:
                    status_message.success("Simple models created successfully!")
                else:
                    status_message.error("Failed to create simple models.")
    
    # Main content area - Display patient data if available
    if hasattr(data_processor, 'patients_data') and data_processor.patients_data:
        # Show patient selection
        patient_ids = list(data_processor.patients_data.keys())
        
        selected_patient = st.selectbox(
            "Select Patient ID",
            patient_ids
        )
        
        if selected_patient:
            st.header(f"Patient {selected_patient}")
            
            try:
                # Process patient data
                patient_data = data_processor.process_patient_data(selected_patient)
                
                if patient_data is None:
                    st.error("Failed to process patient data. Please make sure you've loaded data correctly.")
                else:  # Only proceed if patient_data is not None
                    # Display patient demographics
                    display_patient_demographics(patient_data)
                    
                    # Add quick vitals update panel
                    vitals_updated = display_quick_vitals_update(data_processor, selected_patient)
                    if vitals_updated:
                        # Refresh patient data after update
                        def safe_process_patient_data(data_processor, patient_id):
                            try:
                                return data_processor.process_patient_data(patient_id)
                            except Exception as e:
                                st.error(f"Error refreshing patient data: {str(e)}")
                                import traceback
                                st.text(traceback.format_exc())
                                return None
                        patient_data = safe_process_patient_data(data_processor, selected_patient)
                    
                    # Display vital signs first
                    st.subheader("Vital Signs")

                    # Get available vital signs
                    available_vitals = []
                    if 'timeseries_df' in patient_data and patient_data['timeseries_df'] is not None:
                        df = patient_data['timeseries_df']
                        time_cols = ['time_index', 'timestamp', 'datetime', 'time', 'observationtime']
                        available_vitals = [col for col in df.columns if col not in time_cols]

                    if available_vitals:
                        # Let user select which vitals to display
                        selected_vitals = st.multiselect(
                            "Select vital signs to display",
                            available_vitals,
                            default=available_vitals[:5]  # Default to first 5 vitals
                        )
                        
                        if selected_vitals:
                            # Check if this is a manually created patient
                            is_manual_patient = patient_data.get('is_synthetic', False)
                            
                            # Display the comprehensive vitals dashboard
                            abnormal_vitals = plot_patient_vitals(
                                selected_patient,
                                data_processor,
                                alert_system,
                                alert_enabled,
                                selected_vitals
                            )
                            
                            # Display predictions AFTER vitals, using the abnormal vitals information
                            predictions = display_patient_predictions(model, data_processor, selected_patient, abnormal_vitals)
                            
                            # If abnormal vitals were detected, send alert immediately
                            if abnormal_vitals and alert_enabled and alert_system:
                                # Prepare vitals for alert system
                                vitals_for_alert = {row['Vital Sign']: row['Current Value'] for row in abnormal_vitals}
                                
                                try:
                                    # Create a detailed alert message
                                    alert_message = f"URGENT ALERT for Patient {selected_patient}:\n\n"
                                    for vital in abnormal_vitals:
                                        alert_message += f"- {vital['Vital Sign']} is {vital['Status'].upper()}: {vital['Current Value']} (normal range: {vital['Normal Range']})\n"
                                    
                                    # Add patient demographics if available
                                    if 'demographics' in patient_data and patient_data['demographics']:
                                        alert_message += "\nPatient Information:\n"
                                        for key, value in patient_data['demographics'].items():
                                            if key not in ['patientunitstayid', 'patient_id']:
                                                alert_message += f"- {key}: {value}\n"
                                    
                                    # Add timestamp
                                    import datetime
                                    alert_message += f"\nTimestamp: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                                    
                                    # Send direct email alert
                                    subject = f"URGENT: Patient {selected_patient} Vital Signs Alert"
                                    email_sent = alert_system._send_email(
                                        alert_system.default_recipient_email,
                                        subject,
                                        alert_message
                                    )
                                    
                                    # Also send through regular alert system
                                    alert_system_result = alert_system.check_vitals(selected_patient, vitals_for_alert)
                                    
                                    if email_sent or alert_system_result:
                                        st.success("✅ Alert successfully sent to healthcare providers!")
                                    else:
                                        st.error("❌ Failed to send alert. Please check alert system configuration.")
                                except Exception as e:
                                    st.error(f"Error sending alert: {str(e)}")
                            
                            # Display prediction history
                            if hasattr(data_processor, 'get_prediction_history'):
                                history = data_processor.get_prediction_history(selected_patient)
                                if history and len(history) > 1:
                                    st.subheader("Prediction History")
                                    history_df = pd.DataFrame(history)
                                    
                                    # Convert mortality to percentage
                                    if 'mortality' in history_df.columns:
                                        history_df['mortality'] = history_df['mortality'] * 100
                                    
                                    # Create plotly chart for prediction history
                                    import plotly.graph_objects as go
                                    from plotly.subplots import make_subplots
                                    
                                    fig = make_subplots(rows=2, cols=1, shared_xaxes=True)
                                    
                                    if 'mortality' in history_df.columns:
                                        fig.add_trace(
                                            go.Scatter(x=history_df['timestamp'], y=history_df['mortality'], 
                                                      mode='lines+markers', name='Mortality Risk (%)'),
                                            row=1, col=1
                                        )
                                    
                                    if 'los' in history_df.columns:
                                        fig.add_trace(
                                            go.Scatter(x=history_df['timestamp'], y=history_df['los'], 
                                                      mode='lines+markers', name='Length of Stay (days)'),
                                            row=2, col=1
                                        )
                                    
                                    fig.update_layout(height=500, title_text="Patient Prediction History")
                                    st.plotly_chart(fig, use_container_width=True)
                            
                            # Store prediction in history if not already done
                            if predictions and hasattr(data_processor, 'store_prediction_history'):
                                data_processor.store_prediction_history(selected_patient, predictions)
                            
                            # If abnormal vitals were detected, show a summary
                            if abnormal_vitals:
                                # Calculate risk level based on number and severity of abnormal vitals
                                risk_level = len(abnormal_vitals)
                                
                                if risk_level >= 3:
                                    st.error("🚨 Multiple abnormal vital signs detected. Immediate attention recommended.")
                                elif risk_level >= 1:
                                    st.warning("⚠️ Abnormal vital signs detected. Close monitoring recommended.")
                            else:
                                st.success("✅ All vital signs are within normal ranges.")
                        else:
                            st.info("Please select at least one vital sign to display.")
                            # Still show predictions even if no vitals are selected
                            display_patient_predictions(model, data_processor, selected_patient)
                    else:
                        st.info("No vital signs data available for this patient.")
                        # Still show predictions even if no vitals are available
                        display_patient_predictions(model, data_processor, selected_patient)
            except Exception as e:
                st.error(f"Error processing patient data: {str(e)}")
                import traceback
                st.text(traceback.format_exc())
        else:
            st.info("Please select a patient to view their data.")
    else:
        # Show welcome message
        st.info("👈 Please use the sidebar to load patient data or create a manual patient record.")
        
        st.markdown("""
        ## Welcome to the ICU Patient Data Visualization System
        
        This application allows you to:
        
        1. **Upload patient data** in CSV or ZIP format
        2. **Manually enter patient data** for testing
        3. **Visualize vital signs** and detect abnormalities
        4. **Receive alerts** when vital signs are outside normal ranges
        
        To get started, select an option in the sidebar.
        """)
        
        # Add a prominent button to guide users
        if st.button("Create Manual Patient Record"):
            st.session_state['data_source'] = "Enter patient data manually"
            st.session_state['tab'] = "Data Loading"
            st.experimental_rerun()

if __name__ == "__main__":
    main()
























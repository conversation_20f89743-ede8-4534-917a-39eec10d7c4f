import shap
import numpy as np
import matplotlib.pyplot as plt
import torch

class ModelExplainer:
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
        
    def explain_prediction(self, patient_data, background_data=None):
        """Generate SHAP values to explain model prediction for a patient"""
        # Prepare model wrapper for SHAP
        def model_predict(x):
            with torch.no_grad():
                x_tensor = torch.tensor(x, dtype=torch.float32)
                return self.model(x_tensor).cpu().numpy()
        
        # Create explainer
        if background_data is None:
            explainer = shap.Explainer(model_predict, patient_data)
        else:
            explainer = shap.DeepExplainer(self.model, background_data)
            
        # Calculate SHAP values
        shap_values = explainer.shap_values(patient_data)
        
        # Get top contributing features
        feature_importance = np.abs(shap_values).mean(0)
        top_indices = np.argsort(feature_importance)[-5:]  # Top 5 features
        top_features = [(self.feature_names[i], feature_importance[i]) for i in top_indices]
        
        return {
            'shap_values': shap_values,
            'top_features': top_features
        }